require 'rails_helper'

RSpec.describe Customer, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:dealership) }
    it { is_expected.to have_many(:drives).dependent(:destroy) }
    it { is_expected.to have_one(:driver_license).dependent(:destroy) }
  end

  it { is_expected.to accept_nested_attributes_for(:driver_license) }

  describe 'validations' do
    subject { build(:customer) }

    it { is_expected.to validate_presence_of(:first_name) }
    it { is_expected.to validate_presence_of(:last_name) }

    context 'email format' do
      it 'allows valid email formats' do
        customer = build(:customer, email: '<EMAIL>')
        expect(customer).to be_valid
      end

      it 'rejects invalid email formats' do
        customer = build(:customer, email: 'invalid-email')
        expect(customer).not_to be_valid
      end
    end

    context 'company name' do
      it 'allows valid company name with length between 3 and 255' do
        customer = build(:customer, company_name: 'Test Company')
        expect(customer).to be_valid
      end

      it 'rejects invalid company name with length less than 3' do
        customer = build(:customer, company_name: 'a')
        expect(customer).not_to be_valid
      end

      it 'rejects invalid company name with length more than 255' do
        customer = build(:customer, company_name: 'a' * 256)
        expect(customer).not_to be_valid
      end

      it 'allows blank company name' do
        customer = build(:customer, company_name: nil)
        expect(customer).to be_valid
      end
    end

    context 'phone number' do
      it 'allows valid phone number with length between 6 and 15' do
        customer = build(:customer, phone_number: '123456')
        expect(customer).to be_valid
      end

      it 'rejects invalid phone number with length less than 6' do
        customer = build(:customer, phone_number: '123')
        expect(customer).not_to be_valid
      end

      it 'rejects invalid phone number with length more than 15' do
        customer = build(:customer, phone_number: '1234567890123456')
        expect(customer).not_to be_valid
      end

      it 'rejects blank phone number' do
        customer = build(:customer, phone_number: nil)
        expect(customer).not_to be_valid
      end
    end

    context 'external id' do
      it 'allows valid external id with length between 1 and 50' do
        customer = build(:customer, external_id: '123456')
        expect(customer).to be_valid
      end

      it 'rejects invalid external id with length more than 50' do
        customer = build(:customer, external_id: 'a' * 51)
        expect(customer).not_to be_valid
      end

      it 'allows blank external id' do
        customer = build(:customer, external_id: nil)
        expect(customer).to be_valid
      end
    end
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:gender).with_values(unspecified: 0, male: 1, female: 2, other: 3).backed_by_column_of_type(:integer) }
  end

  describe '#full_name' do
    it 'returns the full name' do
      customer = build(:customer, first_name: 'Jane', last_name: 'Smith')
      expect(customer.full_name).to eq('Jane Smith')
    end
  end
end

require 'rails_helper'

RSpec.describe Drive, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:dealership) }
    it { is_expected.to belong_to(:vehicle) }
    it { is_expected.to belong_to(:trade_plate).optional }
    it { is_expected.to belong_to(:customer) }
    it { is_expected.to belong_to(:sales_person).class_name('User') }
    it { is_expected.to belong_to(:driver_license).optional }
    it { is_expected.to belong_to(:sales_person_accompanying).class_name("User").optional }
    it { is_expected.to have_one(:initial_damage_report).class_name("DamageReport").optional.dependent(:destroy) }
    it { is_expected.to have_one(:final_damage_report).class_name("DamageReport").optional.dependent(:destroy) }
    it { is_expected.to have_many(:waypoints).class_name("GpsLocation").dependent(:destroy) }
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:drive_type).with_values(test_drive: 0, enquiry: 1, loan: 2, appraisal: 3, loan_booking: 4, test_drive_booking: 5, self_loan: 6).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:status).with_values(scheduled: 0, in_progress: 1, completed: 2, cancelled: 3).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:sold_status).with_values(not_sold: 0, sold: 1).backed_by_column_of_type(:integer) }
  end

  describe 'validations' do
    context 'timestamps' do
      it 'ensures start_datetime is not in the future' do
        drive = build(:drive, start_datetime: Time.current + 1.hour)
        expect(drive).not_to be_valid
        expect(drive.errors[:start_datetime]).to include('cannot be in the future')
      end

      it 'ensures end_datetime is not in the future' do
        drive = build(:drive, end_datetime: Time.current + 1.hour)
        expect(drive).not_to be_valid
        expect(drive.errors[:end_datetime]).to include('cannot be in the future')
      end

      it 'ensures end_datetime is after start_datetime' do
        drive = build(:drive, start_datetime: Time.current, end_datetime: Time.current - 1.hour)
        expect(drive).not_to be_valid
        expect(drive.errors[:end_datetime]).to include('must be after start time')
      end
    end

    context 'odometer readings' do
      it 'ensures odometer readings are positive' do
        drive = build(:drive, start_odometer_reading: -1, end_odometer_reading: -1)
        expect(drive).not_to be_valid
        expect(drive.errors[:start_odometer_reading]).to include('must be a positive number')
        expect(drive.errors[:end_odometer_reading]).to include('must be a positive number')
      end

      it 'ensures end odometer reading is greater than start odometer reading' do
        drive = build(:drive, start_odometer_reading: 100, end_odometer_reading: 50)
        expect(drive).not_to be_valid
        expect(drive.errors[:end_odometer_reading]).to include('must be greater than start odometer reading')
      end
    end
  end
end

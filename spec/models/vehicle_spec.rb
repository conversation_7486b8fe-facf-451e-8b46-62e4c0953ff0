require 'rails_helper'

RSpec.describe Vehicle, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:dealership) }
    it { is_expected.to have_many(:drives).dependent(:destroy) }
    it { is_expected.to have_one(:last_damage_report).class_name("DamageReport").optional.dependent(:destroy) }
    it { is_expected.to have_one(:last_known_location).class_name("GpsLocation").dependent(:destroy) }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:make) }
    it { is_expected.to validate_presence_of(:model) }
    it { is_expected.to validate_length_of(:rego).is_at_most(20) }
    it { is_expected.to validate_length_of(:vin).is_at_most(17) }

    it { should validate_numericality_of(:year).is_greater_than(1900) }
    it { should validate_numericality_of(:year).is_less_than_or_equal_to(Date.current.year + 1) }
    it { should validate_presence_of(:year) }
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:status).with_values(available: 0, in_use: 1, out_of_service: 2, deleted: 3, sold: 4, enquiry: 5).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:vehicle_type).with_values(new_vehicle: 0, demo: 1, old: 2).backed_by_column_of_type(:integer) }
  end

   describe 'scopes' do
    let(:dealership) { create(:dealership) }
    let!(:available_vehicle) { create(:vehicle, dealership: dealership, status: :available) }
    let!(:maintenance_vehicle) { create(:vehicle, dealership: dealership, status: :out_of_service) }

    describe '.available_for_test_drive' do
      it 'returns only available vehicles' do
        expect(Vehicle.available_for_test_drive).to include(available_vehicle)
        expect(Vehicle.available_for_test_drive).not_to include(maintenance_vehicle)
      end
    end
  end
  describe '#display_name' do
    it 'returns formatted vehicle name' do
      vehicle = build(:vehicle, year: 2023, make: 'Toyota', model: 'Camry')
      expect(vehicle.display_name).to eq('2023 Toyota Camry')
    end
  end

  describe "photo attachments" do
    it "is valid with multiple image files" do
      vehicle = build(:vehicle, :with_photos)
      expect(vehicle).to be_valid
    end

    it "is invalid with an image larger than 5MB" do
      vehicle = build(:vehicle, :with_photos)
      vehicle.photos.attach(
        io: File.open(Rails.root.join("spec", "fixtures", "files", "large_car_image.png")),
        filename: "large_car_image.png",
        content_type: "image/png"
      )
      expect(vehicle).not_to be_valid
      expect(vehicle.errors[:photos]).to include(a_string_matching(/file size must be less than 5 MB/))
    end

    it "is invalid with an invalid file type" do
      vehicle = build(:vehicle, :with_invalid_photos)
      expect(vehicle).not_to be_valid
      expect(vehicle.errors[:photos]).to include("has an invalid content type (authorized content types are PNG, JPG)")
    end

    it "is invalid with more than 5 photos" do
      vehicle = build(:vehicle)
      6.times do |i|
        vehicle.photos.attach(
          io: StringIO.new("image content #{i}"),
          filename: "image_#{i}.png",
          content_type: "image/png"
        )
      end
      expect(vehicle).not_to be_valid
      expect(vehicle.errors[:photos]).to include("too many files attached (maximum is 5 files, got 6)")
    end
  end

  describe 'year validation' do
    it 'accepts current year' do
      vehicle = build(:vehicle, year: Date.current.year)
      expect(vehicle).to be_valid
    end

    it 'accepts next year' do
      vehicle = build(:vehicle, year: Date.current.year + 1)
      expect(vehicle).to be_valid
    end

    it 'rejects years too far in future' do
      vehicle = build(:vehicle, year: Date.current.year + 2)
      expect(vehicle).not_to be_valid
    end

    it 'rejects years too far in past' do
      vehicle = build(:vehicle, year: 1899)
      expect(vehicle).not_to be_valid
    end
  end

  describe '.not_deleted' do
    let(:dealership) { create(:dealership) }
    let!(:deleted_vehicle) { create(:vehicle, dealership: dealership, status: :deleted) }
    let!(:active_vehicle) { create(:vehicle, dealership: dealership, status: :available) }

    it 'returns vehicles that are not deleted' do
      expect(Vehicle.not_deleted).to include(active_vehicle)
      expect(Vehicle.not_deleted).not_to include(deleted_vehicle)
    end
  end
end

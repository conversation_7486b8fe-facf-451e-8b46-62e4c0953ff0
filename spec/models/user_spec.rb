require "rails_helper"

RSpec.describe User, type: :model do
  subject(:user) do
    described_class.new(
      email: "<EMAIL>",
      password: "Secure@123",
      password_confirmation: "Secure@123",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      phone: "+12345678901",
      preferred_2fa: :email
    )
  end
  describe 'associations' do
    it { is_expected.to have_one(:driver_license).dependent(:destroy) }
    it { should have_many(:device_registrations).dependent(:destroy) }
  end

  describe "validations" do
    it { should validate_presence_of(:email) }
    it do
      # Create a user first with valid attributes to serve as the existing record
      create(:user)
      should validate_uniqueness_of(:email).case_insensitive
    end

    it { should validate_presence_of(:first_name) }
    it { should validate_presence_of(:last_name) }

    it {
      should allow_value("+12345678901").for(:phone)
      should_not allow_value("123456789").for(:phone)
    }

    it "validates OTP if present" do
      user.otp = 123456
      expect(user).to be_valid

      user.otp = 99999
      expect(user).not_to be_valid
      expect(user.errors[:otp]).to include("must be greater than or equal to 100000")
    end
  end

  describe "password complexity" do
    it "is invalid if password is too weak" do
      user.password = "weakpass"
      user.valid?
      expect(user.errors[:password]).to include(
        "requirement not met. Please use: 1 uppercase, 1 lowercase, 1 digit and 1 special character"
      )
    end

    it "is valid with a strong password" do
      user.password = "Strong1@Pass"
      user.password_confirmation = "Strong1@Pass"
      expect(user).to be_valid
    end
  end

  describe "UUID generation" do
    it "generates a uuid before validation on create" do
      user.save!
      expect(user.uuid).to be_present
      expect(user.uuid).to match(/\A[\da-f]{8}-([\da-f]{4}-){3}[\da-f]{12}\z/i)
    end
  end

  describe "preferred_2fa enum" do
    it "has valid values" do
      expect(described_class.preferred_2fas.keys).to include("sms", "email", "totp")
    end

    it "defaults to email" do
      new_user = described_class.new
      expect(new_user.preferred_2fa).to eq("email")
    end
  end

   describe 'associations' do
    it { should have_many(:user_dealerships).dependent(:destroy) }
    it { should have_many(:dealerships).through(:user_dealerships) }
    it { should have_many(:drives).with_foreign_key('sales_person_id') }
  end

  describe 'enums' do
    it do
      should define_enum_for(:user_type)
        .with_values(super_admin: 2, staff: 1, dealership_user: 0)
    end

    it do
      should define_enum_for(:status)
        .with_values(inactive: 0, active: 1, disabled: 2, deleted: 3)
    end

    it do
      should define_enum_for(:preferred_language)
        .with_values(english: 0, spanish: 1)
    end
    it do
      should define_enum_for(:time_zone).backed_by_column_of_type(:string)
    end
  end

   describe '#name' do
    it 'returns the full name' do
      user = build(:user, first_name: 'John', last_name: 'Doe')
      expect(user.name).to eq('John Doe')
    end
  end

  describe '#dealership_role' do
    let(:user) { create(:user) }
    let(:dealership) { create(:dealership) }

    context 'when user has a role at the dealership' do
      before do
        create(:user_dealership, user: user, dealership: dealership, role: :dealership_admin)
      end

      it 'returns the role' do
        expect(user.dealership_role(dealership)).to eq("dealership_admin")
      end
    end

    context 'when user has no role at the dealership' do
      it 'returns nil' do
        expect(user.dealership_role(dealership)).to be_nil
      end
    end
  end

  describe '#admin_of?' do
    let(:user) { create(:user) }
    let(:dealership) { create(:dealership) }

    context 'when user is admin' do
      before do
        create(:user_dealership, user: user, dealership: dealership, role: :dealership_admin)
      end

      it 'returns true' do
        expect(user.dealership_admin?(dealership)).to be true
      end
    end

    context 'when user is not admin' do
      before do
        create(:user_dealership, user: user, dealership: dealership, role: :sales_person)
      end

      it 'returns false' do
        expect(user.dealership_admin?(dealership)).to be false
      end
    end
  end

  describe '#sales_person_of?' do
    let(:user) { create(:user) }
    let(:dealership) { create(:dealership) }

    context 'when user is sales person' do
      before do
        create(:user_dealership, user: user, dealership: dealership, role: :sales_person)
      end

      it 'returns true' do
        expect(user.sales_person?(dealership)).to be true
      end
    end

    context 'when user is not sales person' do
      before do
        create(:user_dealership, user: user, dealership: dealership, role: :dealership_admin)
      end

      it 'returns false' do
        expect(user.sales_person?(dealership)).to be false
      end
    end
  end
end

describe '#login_device' do
    let(:user) { create(:user) }
    let(:device_id) { SecureRandom.uuid }
    let(:app_version) { "1.0.0" }
    let(:app_build_number) { "100" }
    let(:device_os) { "ios" }

    it 'creates a new device registration' do
      expect {
        Auth::DeviceLoginService.call(user, device_id, app_version, app_build_number, device_os)
      }.to change(DeviceRegistration, :count).by(1)
    end

    it 'returns login result with tokens' do
      result = Auth::DeviceLoginService.call(user, device_id, app_version, app_build_number, device_os)

      expect(result).to have_key(:device_registration)
      expect(result).to have_key(:access_token)
      expect(result).to have_key(:refresh_token)
      expect(result).to have_key(:expires_at)
    end

    it 'invalidates existing device session' do
      existing_device = create(:device_registration, user: user, device_id: device_id, active: true)

      Auth::DeviceLoginService.call(user, device_id, app_version, app_build_number, device_os)

      existing_device.reload
      existing_device.invalidate!
      expect(existing_device.active).to be_falsey
    end

    it 'captures request IP from thread' do
      Thread.current[:request_ip] = '***********'

      result = Auth::DeviceLoginService.call(user, device_id, app_version, app_build_number, device_os)

      expect(result[:device_registration].last_login_ip).to eq('***********')
      Thread.current[:request_ip] = nil
    end
  end

  describe '#logout_device' do
    let(:user) { create(:user) }
    let(:device_registration) { create(:device_registration, user: user, active: true) }

    it 'invalidates the specified device' do
      user.logout_device(device_registration.device_id)

      device_registration.reload
      expect(device_registration.active).to be_falsey
    end
  end

  describe '#logout_all_devices' do
    let(:user) { create(:user) }

    it 'invalidates all user devices' do
      devices = create_list(:device_registration, 3, user: user, active: true)

      user.logout_all_devices

      devices.each do |device|
        device.reload
        expect(device.active).to be_falsey
      end
    end
  end

  describe '#generate_temporary_token' do
    let(:user) { create(:user) }

    it 'generates a temporary token with expiry' do
      token, expiry = Auth::TokenService.new(nil, user).generate_temporary_token

      expect(token).to be_present
      expect(expiry).to be_a(Integer)
      expect(expiry).to be > Time.current.to_i
    end
  end
  describe '#active_devices' do
    let(:user) { create(:user) }
    let!(:active_device1) { create(:device_registration, user: user, active: true, created_at: 2.days.ago) }
    let!(:active_device2) { create(:device_registration, user: user, active: true, created_at: 1.day.ago) }
    let!(:inactive_device) { create(:device_registration, user: user, active: false) }

    before do
      allow(DeviceRegistration).to receive(:valid).and_return(DeviceRegistration.where(active: true))
    end

    it 'returns only active devices ordered by created_at desc' do
      expect(user.active_devices).to eq([ active_device2, active_device1 ])
      expect(user.active_devices).not_to include(inactive_device)
    end
  end

  describe '#cleanup_old_devices' do
    let(:user) { create(:user) }
    let!(:old_inactive_device) { create(:device_registration, user: user, active: false, logged_out_at: 100.days.ago) }
    let!(:recent_inactive_device) { create(:device_registration, user: user, active: false, logged_out_at: 10.days.ago) }
    let!(:active_device) { create(:device_registration, user: user, active: true) }

    it 'removes inactive devices older than given days' do
      expect {
        user.cleanup_old_devices(90)
      }.to change { DeviceRegistration.where(id: old_inactive_device.id).count }.from(1).to(0)
      expect(DeviceRegistration.exists?(recent_inactive_device.id)).to be true
      expect(DeviceRegistration.exists?(active_device.id)).to be true
    end
  end

  describe '.password_change_required' do
    let!(:user1) { create(:user, password_change_required: true) }
    let!(:user2) { create(:user, password_change_required: false) }

    it 'returns only users requiring password change' do
      expect(User.password_change_required).to include(user1)
      expect(User.password_change_required).not_to include(user2)
    end
  end

  describe '#mark_password_as_changed!' do
    let(:user) { create(:user, password_change_required: true) }

    it 'sets password_change_required to false' do
      user.mark_password_as_changed!
      expect(user.reload.password_change_required).to be false
    end
  end

  describe '#require_password_change!' do
    let(:user) { create(:user, password_change_required: false) }

    it 'sets password_change_required to true' do
      user.require_password_change!
      expect(user.reload.password_change_required).to be true
    end
  end

  describe '.incomplete_onboarding' do
    let!(:user1) { create(:user, onboarding_completed: true) }
    let!(:user2) { create(:user, onboarding_completed: false) }

    it 'returns only users requiring password change' do
      expect(User.incomplete_onboarding).to include(user2)
      expect(User.incomplete_onboarding).not_to include(user1)
    end
  end

  describe '#mark_onboarding_completed!' do
    let(:user) { create(:user, onboarding_completed: false) }

    it 'sets onboarding_completed to true' do
      user.mark_onboarding_completed!
      expect(user.reload.onboarding_completed).to be true
    end
  end

  describe '#normalize_email' do
    it 'downcases email before validation' do
      user = build(:user, email: '<EMAIL>')
      user.valid?
      expect(user.email).to eq('<EMAIL>')
    end
  end

# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DamageReport, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:drive).optional }
    it { is_expected.to belong_to(:vehicle) }
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:report_type).with_values(initial: 0, final: 1, vehicle: 3) }
  end

  describe 'validations' do
    let(:damage_report) { build(:damage_report, drive: create(:drive)) }
    it 'needs report type to be present when drive is present' do
      expect(damage_report).to_not be_valid
      expect(damage_report.errors[:report_type]).to include("is needed to save damage report for drive")
    end
  end

  describe "media files attachment" do
    context "with multiple files" do
      it "is valid with a mix of image and video files" do
        damage_report = build(:damage_report, :with_media_files)
        expect(damage_report).to be_valid
      end

      it "is invalid when an image is larger than 5MB" do
        damage_report = build(:damage_report)
        damage_report.media_files.attach(
          io: StringIO.new('a' * 6.megabytes),
          filename: "large_image.png",
          content_type: "image/png"
        )
        expect(damage_report).not_to be_valid
        expect(damage_report.errors[:media_files]).to include("size should not exceed 5MB for images")
      end

      it "is invalid when a video is larger than 20MB" do
        damage_report = build(:damage_report)
        damage_report.media_files.attach(
          io: StringIO.new('a' * 21.megabytes),
          filename: "large_video.mp4",
          content_type: "video/mp4"
        )
        expect(damage_report).not_to be_valid
        expect(damage_report.errors[:media_files]).to include("size should not exceed 20MB for videos")
      end

      it "is invalid when any file has an invalid type" do
        damage_report = build(:damage_report)
        damage_report.media_files.attach(
          io: StringIO.new('test'),
          filename: "test.txt",
          content_type: "text/plain"
        )
        expect(damage_report).not_to be_valid
        expect(damage_report.errors[:media_files]).to include("must be an image or video")
      end

      it "is invalid when more than 5 files are attached" do
        damage_report = build(:damage_report)
        6.times do |i|
          damage_report.media_files.attach(
            io: StringIO.new("file content #{i}"),
            filename: "file_#{i}.png",
            content_type: "image/png"
          )
        end
        expect(damage_report).not_to be_valid
        expect(damage_report.errors[:media_files]).to include(a_string_matching(/too many files attached \(maximum is 5 files, got/))
      end
    end
  end
end

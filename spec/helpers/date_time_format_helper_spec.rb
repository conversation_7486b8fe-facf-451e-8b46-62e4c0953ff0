# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DateTimeFormatHelper, type: :helper do
  describe '#parse_date_time' do
    subject(:parse_date_time) { helper.parse_date_time(datetime_string) }

    context 'with valid datetime string' do
      let(:datetime_string) { '2025-06-22T11:58:45+02:00' }

      it 'returns a DateTime object' do
        expect(parse_date_time).to be_a(DateTime)
        expect(parse_date_time.year).to eq(2025)
        expect(parse_date_time.month).to eq(6)
        expect(parse_date_time.day).to eq(22)
        expect(parse_date_time.hour).to eq(11)
        expect(parse_date_time.min).to eq(58)
        expect(parse_date_time.sec).to eq(45)
      end
    end

    context 'with invalid datetime string' do
      let(:datetime_string) { 'invalid' }

      it 'raises ArgumentError' do
        expect { parse_date_time }.to raise_error(ArgumentError)
      end
    end
  end

  describe '#format_iso8601_with_offset' do
    subject(:format_iso8601_with_offset) { helper.format_iso8601_with_offset(datetime) }

    context 'with valid datetime object' do
      let(:datetime) { DateTime.new(2025, 6, 22, 11, 58, 45) }
      let(:expected) { '2025-06-22T11:58:45+00:00' }

      it 'returns formatted ISO8601 string with UTC offset' do
        expect(format_iso8601_with_offset).to eq(expected)
      end
    end

    context 'with nil datetime' do
      let(:datetime) { nil }

      it 'returns nil' do
        expect(format_iso8601_with_offset).to be_nil
      end
    end
  end
end

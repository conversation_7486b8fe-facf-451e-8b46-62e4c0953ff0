require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Users::Documents", type: :request do
  let(:user) { create(:user) }
  let(:device_registration) { create(:device_registration, user: user) }
  let(:valid_token) { Auth::TokenService.new(device_registration).generate_tokens[:access_token] }

  let(:headers) do
    {
      "Authorization" => "Bearer #{valid_token}",
      "Device-ID" => device_registration.device_id,
      "Content-Type" => "application/json"
    }
  end

  let(:Authorization) { "Bearer #{valid_token}" }
  let(:"Device-ID") { device_registration.device_id }

  path "/api/v1/users/me/documents/driving-license" do
    get "Get user's driving license" do
      tags "Documents"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"

      response "200", "Driving license retrieved successfully" do
        let!(:driver_license) { create(:driver_license, holder: user) }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license retrieved successfully")
          expect(data.dig("data", "driving_license", "uuid")).to eq(driver_license.uuid)
        end
      end

      response "404", "Driving license not found" do
        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license not found")
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end

    post "Create or update driving license" do
      tags "Documents"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"

      parameter name: :license_params, in: :body, schema: {
        type: :object,
        properties: {
          licence_number: { type: :string, example: "DL123456789" },
          expiry_date: { type: :string, format: :date, example: "2025-12-31" },
          issue_date: { type: :string, format: :date, example: "2020-01-01" },
          category: { type: :string, example: "C" },
          issuing_country: { type: :string, example: "au" },
          issuing_state: { type: :string, example: "NSW" },
          full_name: { type: :string, example: "John Doe" },
          date_of_birth: { type: :string, format: :date, example: "1990-01-01" }
        },
        required: [ "licence_number", "expiry_date" ]
      }

      response "200", "Driving license created/updated successfully" do
        let(:license_params) do
          {
            licence_number: "DL123456789",
            expiry_date: "2025-12-31",
            issue_date: "2020-01-01",
            category: "C",
            issuing_country: "au",
            issuing_state: "NSW",
            full_name: "John Doe",
            date_of_birth: "1990-01-01"
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to include("successfully")
          expect(data.dig("data", "driving_license", "licence_number")).to eq("DL123456789")
        end
      end

      response "422", "Validation failed" do
        let(:license_params) { { licence_number: "" } }

        run_test! do |response|
          JSON.parse(response.body)
          expect(response.status).to eq(422)
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:license_params) { { licence_number: "DL123456789" } }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end

    delete "Delete driving license" do
      tags "Documents"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"

      response "200", "Driving license deleted successfully" do
        let!(:driver_license) { create(:driver_license, holder: user) }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license deleted successfully")
        end
      end

      response "404", "Driving license not found" do
        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license not found")
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end
  end

  describe "GET /api/v1/users/me/documents/driving-license" do
    subject { get "/api/v1/users/me/documents/driving-license", headers: headers }

    context "when user has a driving license" do
      let!(:driver_license) { create(:driver_license, holder: user) }

      it "returns the driving license successfully" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Driving license retrieved successfully")
        expect(json.dig("data", "driving_license", "uuid")).to eq(driver_license.uuid)
      end
    end

    context "when user does not have a driving license" do
      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Driving license not found")
      end
    end

    context "without authentication" do
      let(:headers) { {} }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "without device ID" do
      let(:headers) do
        {
          "Authorization" => "Bearer #{valid_token}"
        }
      end

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "POST /api/v1/users/me/documents/driving-license" do
    subject { post "/api/v1/users/me/documents/driving-license", params: params.to_json, headers: headers }

    let(:valid_params) do
      {
        licence_number: "DL123456789",
        expiry_date: "2025-12-31",
        issue_date: "2020-01-01",
        category: "C",
        issuing_country: "au",
        issuing_state: "NSW",
        full_name: "John Doe",
        date_of_birth: "1990-01-01"
      }
    end

    context "when creating a new driving license" do
      let(:params) { valid_params }

      it "creates the driving license successfully" do
        expect { subject }.to change(DriverLicense, :count).by(1)

        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to include("created successfully")
        expect(json.dig("data", "driving_license", "licence_number")).to eq("DL123456789")

        user.reload
        expect(user.driver_license.licence_number).to eq("DL123456789")
      end
    end

    context "when updating an existing driving license" do
      let!(:driver_license) do
        create(:driver_license,
               holder: user,
               licence_number: "OLD123",
               expiry_date: Date.current + 1.year,
               full_name: "Old Name",
               date_of_birth: Date.current - 30.years,
               issuing_country: "au")
      end
      let(:params) { valid_params }

      it "updates the driving license successfully" do
        expect { subject }.not_to change(DriverLicense, :count)

        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to include("updated successfully")
        expect(json.dig("data", "driving_license", "licence_number")).to eq("DL123456789")

        driver_license.reload
        expect(driver_license.licence_number).to eq("DL123456789")
      end
    end

    context "with invalid params" do
      let(:params) { { licence_number: "" } }

      it "returns validation errors" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        json = response.parsed_body
        expect(json.dig("status", "code")).to eq(422)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:params) { valid_params }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "without device ID" do
      let(:headers) do
        {
          "Authorization" => "Bearer #{valid_token}",
          "Content-Type" => "application/json"
        }
      end
      let(:params) { valid_params }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "DELETE /api/v1/users/me/documents/driving-license" do
    subject { delete "/api/v1/users/me/documents/driving-license", headers: headers }

    context "when user has a driving license" do
      let!(:driver_license) { create(:driver_license, holder: user) }

      it "deletes the driving license successfully" do
        expect { subject }.to change(DriverLicense, :count).by(-1)

        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Driving license deleted successfully")

        user.reload
        expect(user.driver_license).to be_nil
      end
    end

    context "when user does not have a driving license" do
      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Driving license not found")
      end
    end

    context "without authentication" do
      let(:headers) { {} }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "without device ID" do
      let(:headers) do
        {
          "Authorization" => "Bearer #{valid_token}"
        }
      end

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  path "/api/v1/users/me/documents/driving-license-image" do
    post "Upload driving license image" do
      tags "Documents"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :image_type, in: :formData, type: :string, enum: [ "front", "back" ], required: true, description: "Type of image"
      parameter name: :image, in: :formData, type: :file, required: true, description: "Image file"

      response "200", "Image uploaded successfully" do
        let!(:driver_license) { create(:driver_license, holder: user) }
        let(:image_type) { "front" }
        let(:image) { fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png") }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to include("image uploaded successfully")
          expect(data.dig("data", "driving_license")).to be_present
        end
      end

      response "404", "Driving license not found" do
        let(:image_type) { "front" }
        let(:image) { fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png") }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license not found")
        end
      end

      response "422", "Invalid image type or missing file" do
        let!(:driver_license) { create(:driver_license, holder: user) }
        let(:image_type) { "invalid" }
        let(:image) { fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png") }

        run_test! do |response|
          expect(response.status).to eq(422)
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:image_type) { "front" }
        let(:image) { fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png") }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end

    delete "Delete driving license image" do
      tags "Documents"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :image_type, in: :query, type: :string, enum: [ "front", "back" ], required: true, description: "Type of image to delete"

      response "200", "Image deleted successfully" do
        let!(:driver_license) do
          license = create(:driver_license, holder: user)
          license.front_image.attach(
            io: File.open(Rails.root.join("spec", "fixtures", "files", "test_logo.png")),
            filename: "front.png",
            content_type: "image/png"
          )
          license
        end
        let(:image_type) { "front" }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to include("image deleted successfully")
          expect(data.dig("data", "driving_license")).to be_present
        end
      end

      response "404", "Driving license not found" do
        let(:image_type) { "front" }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license not found")
        end
      end

      response "422", "Image not attached or invalid type" do
        let!(:driver_license) { create(:driver_license, holder: user) }
        let(:image_type) { "front" }

        run_test! do |response|
          expect(response.status).to eq(422)
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:image_type) { "front" }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end
  end

  describe "POST /api/v1/users/me/documents/driving-license-image" do
    subject { post "/api/v1/users/me/documents/driving-license-image", params: params, headers: multipart_headers }

    let!(:driver_license) do
      create(:driver_license,
             holder: user,
             licence_number: "DL123456789",
             expiry_date: Date.current + 1.year,
             full_name: "John Doe",
             date_of_birth: Date.current - 30.years,
             issuing_country: "au")
    end
    let(:image_file) do
      Rack::Test::UploadedFile.new(
        Rails.root.join("spec", "fixtures", "files", "test_logo.png"),
        "image/png"
      )
    end
    let(:multipart_headers) do
      {
        "Authorization" => "Bearer #{valid_token}",
        "Device-ID" => device_registration.device_id
      }
    end

    context "uploading front image" do
      let(:params) { { image_type: "front", image: image_file } }

      it "uploads the image successfully" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to include("Front image uploaded successfully")
        expect(json.dig("data", "driving_license")).to be_present

        driver_license.reload
        expect(driver_license.front_image).to be_attached
      end
    end

    context "uploading back image" do
      let(:params) { { image_type: "back", image: image_file } }

      it "uploads the image successfully" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to include("Back image uploaded successfully")
        expect(json.dig("data", "driving_license")).to be_present

        driver_license.reload
        expect(driver_license.back_image).to be_attached
      end
    end

    context "with invalid image type" do
      let(:params) { { image_type: "invalid", image: image_file } }

      it "returns unprocessable_entity error" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context "without image file" do
      let(:params) { { image_type: "front" } }

      it "returns unprocessable_entity error" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context "when user does not have a driving license" do
      before { driver_license.destroy }
      let(:params) { { image_type: "front", image: image_file } }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Driving license not found")
      end
    end

    context "without authentication" do
      let(:multipart_headers) { {} }
      let(:params) { { image_type: "front", image: image_file } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "without device ID" do
      let(:multipart_headers) do
        {
          "Authorization" => "Bearer #{valid_token}"
        }
      end
      let(:params) { { image_type: "front", image: image_file } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "DELETE /api/v1/users/me/documents/driving-license-image" do
    subject { delete "/api/v1/users/me/documents/driving-license-image", params: params, headers: headers.except("Content-Type") }

    let!(:driver_license) do
      license = create(:driver_license,
                       holder: user,
                       licence_number: "DL123456789",
                       expiry_date: Date.current + 1.year,
                       full_name: "John Doe",
                       date_of_birth: Date.current - 30.years,
                       issuing_country: "au")

      license.front_image.attach(
        io: File.open(Rails.root.join("spec", "fixtures", "files", "test_logo.png")),
        filename: "front.png",
        content_type: "image/png"
      )
      license.back_image.attach(
        io: File.open(Rails.root.join("spec", "fixtures", "files", "test_logo.png")),
        filename: "back.png",
        content_type: "image/png"
      )

      license
    end

    context "deleting front image" do
      let(:params) { { image_type: "front" } }

      it "deletes the image successfully" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to include("Front image deleted successfully")
        expect(json.dig("data", "driving_license")).to be_present

        driver_license.reload
        expect(driver_license.front_image).not_to be_attached
      end
    end

    context "deleting back image" do
      let(:params) { { image_type: "back" } }

      it "deletes the image successfully" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to include("Back image deleted successfully")
        expect(json.dig("data", "driving_license")).to be_present

        driver_license.reload
        expect(driver_license.back_image).not_to be_attached
      end
    end

    context "when image is not attached" do
      before do
        driver_license.front_image.purge
        driver_license.back_image.purge
      end
      let(:params) { { image_type: "front" } }

      it "returns unprocessable_entity error" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context "when user does not have a driving license" do
      before { driver_license.destroy }
      let(:params) { { image_type: "front" } }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Driving license not found")
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:params) { { image_type: "front" } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "without device ID" do
      let(:headers) do
        {
          "Authorization" => "Bearer #{valid_token}"
        }
      end
      let(:params) { { image_type: "front" } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end

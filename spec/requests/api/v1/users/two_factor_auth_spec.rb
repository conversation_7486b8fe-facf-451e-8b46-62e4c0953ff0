require "rails_helper"
require "swagger_helper"

RSpec.describe "Two Factor Authentication", type: :request do
  let(:user) { create(:user) }
  let(:device_registration) { create(:device_registration, user: user) }
  let(:valid_token) { Auth::TokenService.new(device_registration).generate_tokens[:access_token] }
  let(:temporary_token) { "valid.temp.token" }
  let(:mock_expiry) { 5.minutes.from_now }

  let(:headers) do
    {
      "Authorization" => "Bearer #{valid_token}",
      "Device-ID" => device_registration.device_id,
      "App-Version" => "1.0.0",
      "App-Build-Number" => "100",
      "Device-OS" => "iOS"
    }
  end

  let(:Authorization) { "Bearer #{valid_token}" }
  let(:"Device-ID") { device_registration.device_id }
  let(:"App-Version") { "1.0.0" }
  let(:"App-Build-Number") { "100" }
  let(:"Device-OS") { "iOS" }

  let(:mock_setup_result) do
    {
      secret_key: "ABCDEFGHIJKLMNOP",
      qr_code: "data:image/png;base64,QRCodeData",
      qr_code_url: "otpauth://totp/DealerDrive:#{user.email}?secret=ABCDEFGHIJKLMNOP&issuer=DealerDrive"
    }
  end

  let(:mock_login_result) do
    {
      access_token: "new.access.token",
      refresh_token: "new.refresh.token",
      expires_at: 1.hour.from_now.to_i
    }
  end

  path "/api/v1/auth/setup-2fa" do
    post "Setup 2FA with authenticator app" do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"

      response "200", "2FA setup initiated successfully" do
        before do
          allow_any_instance_of(OtpService).to receive(:setup_totp).and_return(mock_setup_result)
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("2FA setup initiated successfully")
          expect(data.dig("data", "secret_key")).to eq(mock_setup_result[:secret_key])
          expect(data.dig("data", "qr_code")).to eq(mock_setup_result[:qr_code])
          expect(data.dig("data", "qr_code_url")).to eq(mock_setup_result[:qr_code_url])
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to include("Invalid token")
        end
      end
    end
  end

  path "/api/v1/auth/verify-2fa-setup" do
    post "Verify 2FA setup" do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :setup_params, in: :body, schema: {
        type: :object,
        properties: {
          otp: { type: :string, example: "123456", description: "6-digit OTP code from authenticator app" }
        },
        required: [ "otp" ]
      }

      response "200", "2FA successfully enabled" do
        let(:setup_params) { { otp: "123456" } }

        before do
          allow(Rails.cache).to receive(:read).with("totp_setup_#{user.id}").and_return(mock_setup_result[:secret_key])
          allow_any_instance_of(OtpService).to receive(:verify_totp_setup).and_return(true)
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("2FA successfully enabled")
        end
      end

      response "401", "Invalid OTP" do
        let(:setup_params) { { otp: "000000" } }

        before do
          allow(Rails.cache).to receive(:read).with("totp_setup_#{user.id}").and_return(mock_setup_result[:secret_key])
          allow_any_instance_of(OtpService).to receive(:verify_totp_setup)
            .and_raise(Errors::AuthenticationError.new("Invalid OTP code"))
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Invalid OTP code")
        end
      end
    end
  end

  path "/api/v1/auth/verify-2fa" do
    post "Verify 2FA and complete login" do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"

      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: "App-Version", in: :header, type: :string, required: true, description: "App version"
      parameter name: "App-Build-Number", in: :header, type: :string, required: true, description: "App build number"
      parameter name: "Device-OS", in: :header, type: :string, required: true, description: "Device operating system"

      parameter name: :verify_params, in: :body, schema: {
        type: :object,
        properties: {
          temporary_token: { type: :string, example: "valid.temp.token", description: "Temporary token from login response" },
          otp: { type: :string, example: "123456", description: "6-digit OTP code" },
          method: { type: :string, example: "email", description: "2FA method (email, sms, or totp)" }
        },
        required: [ "temporary_token", "otp" ]
      }

      response "200", "Login successful" do
        let(:verify_params) { { temporary_token: temporary_token, otp: "123456", method: "email" } }

        before do
          allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
          allow_any_instance_of(OtpService).to receive(:validate_otp).and_return(true)
          allow(Auth::DeviceLoginService).to receive(:call).and_return(mock_login_result)
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Logged in successfully.")
          expect(data.dig("data", "access_token")).to eq(mock_login_result[:access_token])
          expect(data.dig("data", "refresh_token")).to eq(mock_login_result[:refresh_token])
        end
      end

      response "401", "Invalid OTP" do
        let(:verify_params) { { temporary_token: temporary_token, otp: "000000", method: "email" } }

        before do
          allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
          allow_any_instance_of(OtpService).to receive(:validate_otp).and_return(false)
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Invalid OTP")
        end
      end

      response "401", "Missing device info" do
        let(:"Device-ID") { nil }
        let(:verify_params) { { temporary_token: temporary_token, otp: "123456", method: "email" } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Invalid token")
        end
      end
    end
  end

  path "/api/v1/auth/request-2fa-method" do
    post "Request alternative 2FA method" do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"

      parameter name: :request_params, in: :body, schema: {
        type: :object,
        properties: {
          temporary_token: { type: :string, example: "valid.temp.token", description: "Temporary token from login response" },
          method: { type: :string, enum: [ "sms", "email" ], example: "email", description: "Desired 2FA method" }
        },
        required: [ "temporary_token", "method" ]
      }

      response "200", "2FA challenge sent successfully" do
        let(:request_params) { { temporary_token: temporary_token, method: "email" } }

        before do
          allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
          allow_any_instance_of(OtpService).to receive(:generate_and_send_otp)
          allow_any_instance_of(User).to receive(:masked_destination).and_return("m***@example.com")
          allow_any_instance_of(Auth::TokenService).to receive(:generate_temporary_token)
            .and_return([ temporary_token, mock_expiry ])
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("2FA challenge sent successfully.")
          expect(data.dig("data", "temporary_token")).to eq(temporary_token)
          expect(data.dig("data", "challenge_type")).to eq("email")
          expect(data.dig("data", "expires_at")).to eq(mock_expiry.as_json)
        end
      end

      response "422", "Invalid method" do
        let(:request_params) { { temporary_token: temporary_token, method: "invalid" } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Invalid 2FA Method. Must be 'sms' or 'email'")
        end
      end
    end
  end

  # Regular RSpec tests for more detailed scenarios
  describe "POST /api/v1/auth/verify-2fa" do
    subject { post "/api/v1/auth/verify-2fa", params: params, headers: headers }

    let(:params) do
      {
        temporary_token: temporary_token,
        otp: otp_code,
        method: method
      }
    end
    let(:otp_code) { "123456" }
    let(:method) { "email" }

    context "when OTP is valid" do
      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
        allow_any_instance_of(OtpService).to receive(:validate_otp).and_return(true)
        allow(Auth::DeviceLoginService).to receive(:call).and_return(mock_login_result)
      end

      it "completes login" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Logged in successfully.")
        expect(json.dig("data", "access_token")).to eq(mock_login_result[:access_token])
        expect(json.dig("data", "refresh_token")).to eq(mock_login_result[:refresh_token])
      end

      it "clears OTP after successful login" do
        expect_any_instance_of(OtpService).to receive(:clear_code!)
        subject
      end
    end

    context "when OTP is invalid" do
      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
        allow_any_instance_of(OtpService).to receive(:validate_otp).and_return(false)
      end

      it "returns error" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid OTP")
      end
    end

    context "when device info is missing" do
      let(:headers) { { "Authorization" => "Bearer #{valid_token}" } }

      it "returns error" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid token")
      end
    end
  end

  describe "POST /api/v1/auth/request-2fa-method" do
    subject { post "/api/v1/auth/request-2fa-method", params: params }

    let(:params) do
      {
        temporary_token: temporary_token,
        method: method
      }
    end
    let(:method) { "email" }

    context "when requesting a valid 2FA method" do
      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
        allow_any_instance_of(OtpService).to receive(:generate_and_send_otp)
        allow_any_instance_of(User).to receive(:masked_destination).and_return("m***@example.com")
        allow_any_instance_of(Auth::TokenService).to receive(:generate_temporary_token)
          .and_return([ temporary_token, mock_expiry ])
      end

      it "sends OTP and returns success" do
        expect_any_instance_of(OtpService).to receive(:generate_and_send_otp).with(method)

        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("2FA challenge sent successfully.")
        expect(json.dig("data", "temporary_token")).to eq(temporary_token)
        expect(json.dig("data", "challenge_type")).to eq(method)
        expect(json.dig("data", "expires_at")).to eq(mock_expiry.as_json)
      end

      context "when user has TOTP configured" do
        before do
          user.update!(totp_secret: "ABCDEFGHIJKLMNOP")
        end

        it "includes totp in available methods" do
          subject
          expect(response).to have_http_status(:ok)
          json = response.parsed_body

          expect(json.dig("data", "two_factor_methods")).to contain_exactly("sms", "email", "totp")
        end
      end
    end

    context "when method is invalid" do
      let(:method) { "invalid" }

      it "returns error" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid 2FA Method. Must be 'sms' or 'email'")
      end
    end

    context "when token is expired" do
      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token)
          .and_raise(Errors::TokenExpired.new("Token has expired"))
      end

      it "returns error" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Token has expired")
      end
    end
  end

  describe "POST /api/v1/auth/verify-2fa-setup" do
    subject { post "/api/v1/auth/verify-2fa-setup", params: params, headers: headers }

    let(:params) { { otp: otp_code } }
    let(:otp_code) { "123456" }

    context "when OTP is valid" do
      before do
        allow_any_instance_of(OtpService).to receive(:verify_totp_setup).and_return(true)
      end

      it "enables 2FA" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("2FA successfully enabled")
      end
    end

    context "when OTP is invalid" do
      before do
        allow_any_instance_of(OtpService).to receive(:verify_totp_setup)
          .and_raise(Errors::AuthenticationError.new("Invalid OTP code"))
      end

      it "returns error" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid OTP code")
      end
    end

    context "when OTP is missing" do
      let(:params) { {} }

      it "returns error" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("OTP code is required")
      end
    end

    context "when user is not authenticated" do
      let(:headers) { {} }

      it "returns unauthorized error" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end

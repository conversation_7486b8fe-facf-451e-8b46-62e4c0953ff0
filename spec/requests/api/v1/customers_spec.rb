require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Customers", type: :request do
  let(:user) { create(:user) }
  let(:dealership) { create(:dealership) }
  let(:device_registration) { create(:device_registration, user: user) }
  let(:valid_token) { Auth::TokenService.new(device_registration).generate_tokens[:access_token] }

  let(:headers) do
    {
      "Authorization" => "Bearer #{valid_token}",
      "Device-ID" => device_registration.device_id,
      "Content-Type" => "application/json",
      "Host" => "localhost"
    }
  end

  let(:Authorization) { "Bearer #{valid_token}" }
  let(:"Device-ID") { device_registration.device_id }

  before do
    create(:user_dealership, user: user, dealership: dealership, role: :dealership_admin)
  end

  path "/api/v1/dealerships/{dealership_uuid}/customers" do
    get "Get customers list with optional search" do
      tags "Customers"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :page, in: :query, type: :integer, required: false, description: "Page number (default: 1)"
      parameter name: :per_page, in: :query, type: :integer, required: false, description: "Items per page (default: 20, max: 100)"

      response "200", "Customers retrieved successfully" do
        let(:dealership_uuid) { dealership.uuid }
        let!(:customer1) { create(:customer, dealership: dealership, first_name: "Alice", last_name: "Johnson") }
        let!(:customer2) { create(:customer, dealership: dealership, first_name: "Bob", last_name: "Smith") }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customers retrieved successfully")
          customers = data.dig("data", "customers")
          expect(customers).to be_an(Array)
          expect(customers.size).to eq(2)
          expect(customers.map { |c| c["first_name"] }).to eq([ "Alice", "Bob" ])
          expect(response.headers['X-Current-Page']).to be_present
          expect(response.headers['X-Per-Page']).to be_present
          expect(response.headers['X-Total-Count']).to be_present
          expect(response.headers['X-Total-Pages']).to be_present
          expect(response.headers['X-Next-Page']).to be_nil
          expect(response.headers['X-Prev-Page']).to be_nil
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:dealership_uuid) { dealership.uuid }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end

      response "404", "Dealership not found" do
        let(:dealership_uuid) { "invalid-uuid" }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end
    end

    post "Create customer" do
      tags "Customers"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"

      parameter name: :customer_params, in: :body, schema: {
        type: :object,
        properties: {
          first_name: { type: :string, description: "Customer first name" },
          last_name: { type: :string, description: "Customer last name" },
          email: { type: :string, description: "Customer email" },
          phone_number: { type: :string, description: "Customer phone number" },
          age: { type: :integer, description: "Customer age" },
          gender: { type: :string, enum: [ "unspecified", "male", "female", "other" ], description: "Customer gender" },
          company_name: { type: :string, description: "Company name" },
          external_id: { type: :string, description: "External ID" },
          postcode: { type: :string, description: "Postcode" },
          suburb: { type: :string, description: "Suburb" },
          address_line1: { type: :string, description: "Address line 1" },
          address_line2: { type: :string, description: "Address line 2" },
          city: { type: :string, description: "City" },
          state: { type: :string, description: "State" },
          country: { type: :string, description: "Country" }
        },
        required: [ "first_name", "last_name", "email", "phone_number" ]
      }

      response "200", "Customer created successfully" do
        let(:dealership_uuid) { dealership.uuid }
        let(:customer_params) do
          {
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
            phone_number: "+61400000000",
            age: 30,
            gender: "male"
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customer created successfully")
          expect(data.dig("data", "customer")).to be_present
        end
      end

      response "422", "Validation errors" do
        let(:dealership_uuid) { dealership.uuid }
        let(:customer_params) do
          {
            first_name: "",
            last_name: "",
            email: "invalid-email"
          }
        end

        run_test! do |response|
          expect(response.status).to eq(422)
        end
      end

      response "422", "Duplicate email error" do
        let(:dealership_uuid) { dealership.uuid }
        let!(:existing_customer) { create(:customer, dealership: dealership, email: "<EMAIL>") }
        let(:customer_params) do
          {
            first_name: "Jane",
            last_name: "Doe",
            email: "<EMAIL>",
            phone_number: "+61400000001"
          }
        end

        run_test! do |response|
          expect(response.status).to eq(422)
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to include("Customer with this email already exists")
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:dealership_uuid) { dealership.uuid }
        let(:customer_params) do
          {
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
            phone_number: "+61400000000"
          }
        end

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end

      response "404", "Dealership not found" do
        let(:dealership_uuid) { "invalid-uuid" }
        let(:customer_params) do
          {
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
            phone_number: "+61400000000"
          }
        end

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end
    end
  end

  path "/api/v1/dealerships/{dealership_uuid}/customers/search" do
    get "Get customers list with search for autocomplete feature" do
      tags "Customers"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :query, in: :query, type: :string, required: false, description: "Search term (3-20 characters) for name, email, or phone"

      response "200", "Customer search completed successfully" do
        let(:dealership_uuid) { dealership.uuid }
        let(:query) { "Alice" }
        let!(:customer1) { create(:customer, dealership: dealership, first_name: "Alice", last_name: "Johnson") }
        let!(:customer2) { create(:customer, dealership: dealership, first_name: "Bob", last_name: "Smith") }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customer search completed successfully")
          expect(data.dig("data", "customers")).to be_an(Array)
          customer = data.dig("data", "customers").first
          expect(customer.keys).to contain_exactly("uuid", "full_name", "email", "phone_number", "created_at")
        end
      end

      response "200", "Empty search results for short query" do
        let(:dealership_uuid) { dealership.uuid }
        let(:query) { "Al" }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customer search completed successfully")
          expect(data.dig("data", "customers")).to be_empty
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:dealership_uuid) { dealership.uuid }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end

      response "404", "Dealership not found" do
        let(:dealership_uuid) { "invalid-uuid" }
        let(:query) { "Alice" }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end
    end
  end

  path "/api/v1/dealerships/{dealership_uuid}/customers/{id}" do
    get "Get customer details" do
      tags "Customers"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :id, in: :path, type: :string, required: true, description: "Customer UUID"

      response "200", "Customer retrieved successfully" do
        let(:dealership_uuid) { dealership.uuid }
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:id) { customer.uuid }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customer retrieved successfully")
          expect(data.dig("data", "customer")).to be_present
        end
      end

      response "404", "Customer not found" do
        let(:dealership_uuid) { dealership.uuid }
        let(:id) { "invalid-uuid" }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:dealership_uuid) { dealership.uuid }
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:id) { customer.uuid }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end

    put "Update customer" do
      tags "Customers"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :id, in: :path, type: :string, required: true, description: "Customer UUID"

      parameter name: :customer_params, in: :body, schema: {
        type: :object,
        properties: {
          first_name: { type: :string, description: "Customer first name" },
          last_name: { type: :string, description: "Customer last name" },
          email: { type: :string, description: "Customer email" },
          phone_number: { type: :string, description: "Customer phone number" },
          age: { type: :integer, description: "Customer age" },
          gender: { type: :string, enum: [ "unspecified", "male", "female", "other" ], description: "Customer gender" },
          company_name: { type: :string, description: "Company name" },
          external_id: { type: :string, description: "External ID" },
          postcode: { type: :string, description: "Postcode" },
          suburb: { type: :string, description: "Suburb" },
          address_line1: { type: :string, description: "Address line 1" },
          address_line2: { type: :string, description: "Address line 2" },
          city: { type: :string, description: "City" },
          state: { type: :string, description: "State" },
          country: { type: :string, description: "Country" }
        }
      }

      response "200", "Customer updated successfully" do
        let(:dealership_uuid) { dealership.uuid }
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:id) { customer.uuid }
        let(:customer_params) { { first_name: "Updated Name" } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customer updated successfully")
          expect(data.dig("data", "customer", "first_name")).to eq("Updated Name")
        end
      end

      response "404", "Customer not found" do
        let(:dealership_uuid) { dealership.uuid }
        let(:id) { "invalid-uuid" }
        let(:customer_params) { { first_name: "Jane" } }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:dealership_uuid) { dealership.uuid }
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:id) { customer.uuid }
        let(:customer_params) { { first_name: "Jane" } }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/customers" do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/customers", params: params, headers: headers }

    let!(:customers) { create_list(:customer, 25, dealership: dealership) }

    context "with valid dealership uuid" do
      let(:params) { {} }

      it "returns all customers ordered by first name" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customers retrieved successfully")
        customers = json.dig("data", "customers")
        expect(customers.size).to eq(20)  # Default per_page
      end

      it "includes enhanced Pagy pagination metadata" do
        subject

        expect(response.headers['X-Current-Page']).to eq('1')
        expect(response.headers['X-Per-Page']).to eq('20')
        expect(response.headers['X-Total-Count']).to eq('25')
        expect(response.headers['X-Total-Pages']).to eq('2')
        expect(response.headers['X-Next-Page']).to eq('2')
        expect(response.headers['X-Prev-Page']).to be_nil
      end
    end

    context "with pagination parameters" do
      let(:params) { { page: 3, per_page: 10 } }

      it "returns paginated results" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        customers = json.dig("data", "customers")
        expect(customers.size).to eq(5)

        expect(response.headers['X-Current-Page']).to eq('3')
        expect(response.headers['X-Per-Page']).to eq('10')
        expect(response.headers['X-Total-Count']).to eq('25')
        expect(response.headers['X-Total-Pages']).to eq('3')
        expect(response.headers['X-Next-Page']).to be_nil
        expect(response.headers['X-Prev-Page']).to eq('2')
      end
    end

    context "with per_page exceeding maximum" do
      let(:params) { { per_page: 150 } }

      it "limits per_page to maximum allowed" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.headers['X-Per-Page']).to eq('100')
      end
    end

    context "with invalid dealership uuid" do
      subject { get "/api/v1/dealerships/invalid-uuid/customers", headers: headers }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:params) { {} }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/customers/search" do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/customers/search", params: params, headers: headers }

    let!(:customer1) { create(:customer, dealership: dealership, first_name: "Alice", last_name: "Johnson", email: "<EMAIL>") }
    let!(:customer2) { create(:customer, dealership: dealership, first_name: "Bob", last_name: "Smith", email: "<EMAIL>") }
    let!(:customer3) { create(:customer, dealership: dealership, first_name: "Charlie", last_name: "Brown", phone_number: "+61400123456") }

    context "with search query" do
      let(:params) { { query: "alice" } }

      it "returns matching customers with limited fields" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer search completed successfully")
        customers = json.dig("data", "customers")
        expect(customers.size).to eq(1)

        customer = customers.first
        expect(customer.keys).to contain_exactly("uuid", "full_name", "email", "phone_number", "created_at")
        expect(customer["full_name"]).to include("Alice")
      end
    end

    context "with search query containing whitespace" do
      let(:params) { { query: "  alice  " } }

      it "strips whitespace and returns results" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        customers = json.dig("data", "customers")
        expect(customers.size).to eq(1)
        expect(customers.first["full_name"]).to include("Alice")
      end
    end

    context "with case insensitive search" do
      let(:params) { { query: "ALICE" } }

      it "finds results regardless of case" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        customers = json.dig("data", "customers")
        expect(customers.size).to eq(1)
        expect(customers.first["full_name"]).to include("Alice")
      end
    end

    context "with short search query (less than 3 characters)" do
      let(:params) { { query: "Al" } }

      it "returns empty results" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer search completed successfully")
        customers = json.dig("data", "customers")
        expect(customers).to be_empty
      end
    end

    context "with long search query (more than 20 characters)" do
      let(:params) { { query: "a" * 25 } }

      it "returns empty results" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer search completed successfully")
        customers = json.dig("data", "customers")
        expect(customers).to be_empty
      end
    end

    context "with invalid dealership uuid" do
      subject { get "/api/v1/dealerships/invalid-uuid/customers/search", params: params, headers: headers }
      let(:params) { { query: "Alice" } }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without dealership uuid" do
      subject { get "/api/v1/dealerships/customers/search", params: params, headers: headers }
      let(:params) { { query: "Alice" } }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:params) { { dealership_uuid: dealership.uuid } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "with more than 20 records" do
      let!(:customers) { create_list(:customer, 30, dealership: dealership, first_name: 'Alice') }
      let(:params) { { query: "alice" } }

      it "limits results to 20" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        customers = json.dig("data", "customers")
        expect(customers.size).to eq(20)
      end
    end
  end

  describe "POST /api/v1/dealerships/:dealership_uuid/customers" do
    subject { post "/api/v1/dealerships/#{dealership.uuid}/customers", params: params.to_json, headers: headers }

    let(:valid_params) do
      {
        first_name: "John",
        last_name: "Doe",
        email: "<EMAIL>",
        phone_number: "+61400000000",
        age: 30,
        gender: "male"
      }
    end

    context "with valid parameters" do
      let(:params) { valid_params }

      it "creates a new customer" do
        expect { subject }.to change(Customer, :count).by(1)

        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Customer created successfully")
        expect(json.dig("data", "customer", "first_name")).to eq("John")
      end
    end

    context "with invalid parameters" do
      let(:params) { valid_params.merge(email: "invalid-email") }

      it "returns validation errors" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context "with duplicate email" do
      let!(:existing_customer) { create(:customer, dealership: dealership, email: "<EMAIL>") }
      let(:params) { valid_params.merge(email: "<EMAIL>") }

      it "prevents duplicate email creation" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        json = response.parsed_body
        expect(json.dig("status", "message")).to include("Customer with this email already exists")
      end
    end

    context "with invalid dealership uuid" do
      subject { post "/api/v1/dealerships/invalid-uuid/customers", params: params.to_json, headers: headers }
      let(:params) { valid_params }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:params) { valid_params }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/customers/:id" do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/customers/#{customer_id}", headers: headers }

    let!(:customer) { create(:customer, dealership: dealership) }

    context "with valid customer ID" do
      let(:customer_id) { customer.uuid }

      it "returns the customer details" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer retrieved successfully")
        expect(json.dig("data", "customer", "uuid")).to eq(customer.uuid)
      end
    end

    context "with invalid customer ID" do
      let(:customer_id) { "invalid-uuid" }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with customer from different dealership" do
      let(:other_dealership) { create(:dealership) }
      let(:other_customer) { create(:customer, dealership: other_dealership) }
      let(:customer_id) { other_customer.uuid }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:customer_id) { customer.uuid }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "PUT /api/v1/dealerships/:dealership_uuid/customers/:id" do
    subject { put "/api/v1/dealerships/#{dealership.uuid}/customers/#{customer_id}", params: params.to_json, headers: headers }

    let!(:customer) { create(:customer, dealership: dealership, first_name: "Original") }

    context "with valid parameters" do
      let(:customer_id) { customer.uuid }
      let(:params) { { first_name: "Updated" } }

      it "updates the customer" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer updated successfully")
        expect(json.dig("data", "customer", "first_name")).to eq("Updated")
      end
    end

    context "with invalid customer ID" do
      let(:customer_id) { "invalid-uuid" }
      let(:params) { { first_name: "Jane" } }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with customer from different dealership" do
      let(:other_dealership) { create(:dealership) }
      let(:other_customer) { create(:customer, dealership: other_dealership) }
      let(:customer_id) { other_customer.uuid }
      let(:params) { { first_name: "Jane" } }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:customer_id) { customer.uuid }
      let(:params) { { first_name: "Jane" } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end

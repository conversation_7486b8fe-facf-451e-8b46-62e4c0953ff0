require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::TradePlatesController", type: :request do
  let(:user) { create(:user) }
  let(:dealership) { create(:dealership) }
  let!(:user_dealership) { create(:user_dealership, user: user, dealership: dealership, role: :dealership_admin) }
  let(:device_registration) { create(:device_registration, user: user) }
  let(:valid_token) { Auth::TokenService.new(device_registration).generate_tokens[:access_token] }

  let(:headers) do
    {
      "Authorization" => "Bearer #{valid_token}",
      "Device-ID" => device_registration.device_id
    }
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/trade-plates" do
    let!(:active_trade_plate) { create(:trade_plate, dealership: dealership, status: :active, expiry: 1.month.from_now) }
    let!(:expired_trade_plate) { create(:trade_plate, dealership: dealership, status: :active, expiry: 1.day.ago) }
    let!(:inactive_trade_plate) { create(:trade_plate, dealership: dealership, status: :inactive) }

    # Create a trade plate that's in use
    let!(:in_use_trade_plate) { create(:trade_plate, dealership: dealership, status: :active) }
    let!(:vehicle) { create(:vehicle, dealership: dealership) }
    let!(:customer) { create(:customer, dealership: dealership) }
    let!(:in_progress_drive) do
      create(:drive,
             dealership: dealership,
             vehicle: vehicle,
             customer: customer,
             trade_plate: in_use_trade_plate,
             status: :in_progress)
    end

    context "when user has access to dealership" do
      it "returns all trade plates for the dealership with correct flags" do
        get "/api/v1/dealerships/#{dealership.uuid}/trade-plates", headers: headers

        expect(response).to have_http_status(:ok)

        response_data = response.parsed_body
        expect(response_data["status"]["message"]).to eq("Trade plates retrieved successfully")

        trade_plates = response_data["data"]["trade_plates"]
        expect(trade_plates.length).to eq(4)

        # Check active trade plate
        active_plate = trade_plates.find { |tp| tp["uuid"] == active_trade_plate.uuid }
        expect(active_plate["status"]).to eq("active")
        expect(active_plate["expired"]).to be false
        expect(active_plate["in_use"]).to be false

        # Check expired trade plate
        expired_plate = trade_plates.find { |tp| tp["uuid"] == expired_trade_plate.uuid }
        expect(expired_plate["expired"]).to be true
        expect(expired_plate["in_use"]).to be false

        # Check inactive trade plate
        inactive_plate = trade_plates.find { |tp| tp["uuid"] == inactive_trade_plate.uuid }
        expect(inactive_plate["status"]).to eq("inactive")

        # Check in-use trade plate
        in_use_plate = trade_plates.find { |tp| tp["uuid"] == in_use_trade_plate.uuid }
        expect(in_use_plate["in_use"]).to be true
      end
    end

    context "when user doesn't have access to dealership" do
      let(:other_dealership) { create(:dealership) }

      it "returns not found error" do
        get "/api/v1/dealerships/#{other_dealership.uuid}/trade-plates", headers: headers

        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body["status"]["message"]).to eq("Dealership not found or you don't have access to it")
      end
    end

    context "when dealership doesn't exist" do
      it "returns not found error" do
        get "/api/v1/dealerships/non-existent-uuid/trade-plates", headers: headers

        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body["status"]["message"]).to eq("Dealership not found or you don't have access to it")
      end
    end

    context "when user is not authenticated" do
      it "returns unauthorized error" do
        get "/api/v1/dealerships/#{dealership.uuid}/trade-plates"

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  path '/api/v1/dealerships/{dealership_uuid}/trade-plates' do
    parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'

    get('List trade plates for a dealership') do
      tags 'Trade Plates'
      description 'Retrieves all trade plates for a specific dealership. Returns flags indicating if each trade plate is expired or currently in use by an in-progress drive.'
      operationId 'getTradePlates'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token for authentication'
      parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device ID for the request'

      response(200, 'successful') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Trade plates retrieved successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     trade_plates: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                           number: { type: :string, example: 'TP001' },
                           expiry: { type: :string, format: :date, example: '2024-12-31', nullable: true },
                           status: { type: :string, enum: [ 'active', 'inactive' ], example: 'active' },
                           expired: { type: :boolean, example: false, description: 'Flag indicating if the trade plate is expired' },
                           in_use: { type: :boolean, example: false, description: 'Flag indicating if the trade plate is currently being used by an in-progress drive' },
                           created_at: { type: :string, format: 'date-time', example: '2023-01-01T12:00:00Z' },
                           updated_at: { type: :string, format: 'date-time', example: '2023-01-01T12:00:00Z' }
                         },
                         required: [ 'uuid', 'number', 'status', 'expired', 'in_use', 'created_at', 'updated_at' ]
                       }
                     }
                   },
                   required: [ 'trade_plates' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:user) { create(:user) }
        let(:dealership) { create(:dealership) }
        let!(:user_dealership) { create(:user_dealership, user: user, dealership: dealership, role: :dealership_admin) }
        let(:device_registration) { create(:device_registration, user: user) }
        let(:valid_token) { Auth::TokenService.new(device_registration).generate_tokens[:access_token] }
        let(:dealership_uuid) { dealership.uuid }
        let(:Authorization) { "Bearer #{valid_token}" }
        let(:'Device-ID') { device_registration.device_id }

        let!(:active_trade_plate) { create(:trade_plate, dealership: dealership, status: :active, expiry: 1.month.from_now) }
        let!(:expired_trade_plate) { create(:trade_plate, dealership: dealership, status: :active, expiry: 1.day.ago) }
        let!(:inactive_trade_plate) { create(:trade_plate, dealership: dealership, status: :inactive) }

        # Create a trade plate that's in use
        let!(:in_use_trade_plate) { create(:trade_plate, dealership: dealership, status: :active) }
        let!(:vehicle) { create(:vehicle, dealership: dealership) }
        let!(:customer) { create(:customer, dealership: dealership) }
        let!(:in_progress_drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle,
                 customer: customer,
                 trade_plate: in_use_trade_plate,
                 status: :in_progress)
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['status']['message']).to eq('Trade plates retrieved successfully')
          expect(data['data']['trade_plates'].length).to eq(4)

          # Verify flags are correctly set
          trade_plates = data['data']['trade_plates']

          active_plate = trade_plates.find { |tp| tp['uuid'] == active_trade_plate.uuid }
          expect(active_plate['expired']).to be false
          expect(active_plate['in_use']).to be false

          expired_plate = trade_plates.find { |tp| tp['uuid'] == expired_trade_plate.uuid }
          expect(expired_plate['expired']).to be true
          expect(expired_plate['in_use']).to be false

          in_use_plate = trade_plates.find { |tp| tp['uuid'] == in_use_trade_plate.uuid }
          expect(in_use_plate['in_use']).to be true
        end
      end

      response(401, 'unauthorized') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Missing authorization token' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]

        let(:dealership_uuid) { create(:dealership).uuid }
        let(:Authorization) { nil }
        let(:'Device-ID') { nil }

        run_test!
      end

      response(404, 'not found') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Dealership not found or you don\'t have access to it' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]

        let(:user) { create(:user) }
        let(:device_registration) { create(:device_registration, user: user) }
        let(:valid_token) { Auth::TokenService.new(device_registration).generate_tokens[:access_token] }
        let(:dealership_uuid) { 'non-existent-uuid' }
        let(:Authorization) { "Bearer #{valid_token}" }
        let(:'Device-ID') { device_registration.device_id }

        run_test!
      end
    end
  end
end

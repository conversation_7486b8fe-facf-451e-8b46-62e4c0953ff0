require "rails_helper"

RSpec.describe "Api::V1::UsersController", type: :request do
  let(:user) { create(:user) }
  let(:device_registration) { create(:device_registration, user: user) }
  let(:valid_token) { Auth::TokenService.new(device_registration).generate_tokens[:access_token] }

  let(:headers) do
    {
      "Authorization" => "Bearer #{valid_token}",
      "Device-ID" => device_registration.device_id
    }
  end

  shared_examples "unauthorized request" do |message|
    it "returns unauthorized with message: #{message}" do
      subject
      expect(response).to have_http_status(:unauthorized)
      expect(response.parsed_body.dig("status", "message")).to eq(message)
    end
  end

  describe "POST /api/v1/auth/forgot-password" do
    subject { post "/api/v1/auth/forgot-password", params: params }

    let(:params) { { email: user.email } }
    let(:mock_token) { "mock-temporary-token" }
    let(:mock_expiry_time) { 1.hour.from_now.to_i }

    before do
      allow(PasswordService).to receive(:new)
        .and_return(instance_double(PasswordService, generate_and_send_reset_code: true))

      allow_any_instance_of(Auth::TokenService)
        .to receive(:generate_temporary_token)
        .and_return([ mock_token, mock_expiry_time ])
    end

    context "when the email is valid" do
      it "returns temporary token and success message" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Reset password code sent successfully")
        expect(json.dig("data", "temporary_token")).to eq(mock_token)
        expect(json.dig("data", "expires_at")).to be > 0
      end
    end

    context "when the user is not found" do
      let(:params) { { email: "<EMAIL>" } }

      it "returns 404 with user not found message" do
        subject
        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body.dig("status", "message")).to eq("User not found")
      end
    end
  end

  describe "POST /api/v1/auth/verify-reset-code" do
    let(:valid_code) { "123456" }
    let(:temporary_token) { Auth::TokenService.new(nil, user).generate_temporary_token.first }

    subject do
      post "/api/v1/auth/verify-reset-code", params: {
        temporary_token: token,
        reset_code: code
      }
    end

    before do
      allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
      allow_any_instance_of(Auth::TokenService).to receive(:generate_temporary_token).and_return([ "reset.token", 15.minutes.from_now.to_i ])
      allow_any_instance_of(PasswordService).to receive(:clear_code!)
    end

    context "with valid token and code" do
      let(:token) { temporary_token }
      let(:code) { valid_code }

      before do
        allow_any_instance_of(PasswordService).to receive(:validate_security_code).and_return(true)
      end

      it "returns success with reset token" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("Reset Code verified successfully.")
      end
    end

    context "with missing token or code" do
      context "missing token" do
        let(:token) { nil }
        let(:code) { valid_code }

        it "returns unprocessable entity" do
          subject
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "missing code" do
        let(:token) { temporary_token }
        let(:code) { nil }

        it "returns unprocessable entity" do
          subject
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end
    end

    context "with invalid or expired token" do
      let(:token) { "invalid.token" }
      let(:code) { valid_code }

      it "returns unauthorized for invalid token" do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_raise(Errors::InvalidToken)
        subject
        expect(response).to have_http_status(:unauthorized)
      end

      it "returns unauthorized for expired token" do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_raise(Errors::TokenExpired)
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "with invalid reset code" do
      let(:token) { temporary_token }
      let(:code) { "wrong-code" }

      before do
        allow_any_instance_of(PasswordService).to receive(:validate_security_code).and_return(false)
      end

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "POST /api/v1/auth/reset-password" do
    let(:new_password) { "new_secure_password" }
    let(:temporary_token) { Auth::TokenService.new(nil, user).generate_temporary_token.first }

    subject do
      post "/api/v1/auth/reset-password", params: {
        temporary_token: token,
        new_password: password
      }
    end

    before do
      allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
    end

    context "with valid token and password" do
      let(:token) { temporary_token }
      let(:password) { new_password }

      before {
        allow(user).to receive(:update!).with(password: password).and_return(true)
        allow(user).to receive(:mark_password_as_changed!).and_return(true)
      }

      it "resets password successfully" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("Password reset successful.")
      end
    end

    context "with missing params" do
      context "missing token" do
        let(:token) { nil }
        let(:password) { new_password }

        it "returns unprocessable" do
          subject
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "missing password" do
        let(:token) { temporary_token }
        let(:password) { nil }

        it "returns unprocessable" do
          subject
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end
    end

    context "with invalid or expired token" do
      let(:token) { "bad.token" }
      let(:password) { new_password }

      it "returns unauthorized for invalid token" do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_raise(Errors::InvalidToken)
        subject
        expect(response).to have_http_status(:unauthorized)
      end

      it "returns unauthorized for expired token" do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_raise(Errors::TokenExpired)
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when new password matches the current password" do
      let(:token) { temporary_token }
      let(:password) { new_password }

      before do
        allow(user).to receive(:valid_password?).with(password).and_return(true)
      end

      it "returns 422 unprocessable with same password error" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("New password cannot be the same as the current password")
      end
    end

    context "when new password is weak" do
      let(:token) { temporary_token }
      let(:password) { new_password }

      it "returns 422 with validation error" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to include(
          "Password requirement not met. Please use: 1 uppercase, 1 lowercase, 1 digit and 1 special character"
        )
      end
    end
  end

  describe "PUT /api/v1/auth/change-password" do
    subject { put "/api/v1/auth/change-password", params: params, headers: headers }

    let(:params) do
      {
        current_password: "Drive@2025",
        new_password: "NewPass@456"
      }
    end

    context "when current password is incorrect" do
      let(:params) do
        {
          current_password: "WrongPass",
          new_password: "NewPass@456"
        }
      end

      it "returns 422 with validation error" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Incorrect current password")
      end
    end

    context "when new password matches current password" do
      let(:params) do
        {
          current_password: "Drive@2025",
          new_password: "Drive@2025"
        }
      end

      it "returns 422 with validation error" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("New password cannot be the same as the current password")
      end
    end

    context "when new password is weak" do
      let(:params) do
        {
          current_password: "Drive@2025",
          new_password: "weak"
        }
      end

      it "returns 422 with validation error" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to include(
          "Password requirement not met. Please use: 1 uppercase, 1 lowercase, 1 digit and 1 special character"
        )
      end
    end
  end

  describe "GET /api/v1/profile" do
    subject { get "/api/v1/profile", headers: request_headers }

    context "when authorized" do
      let(:request_headers) { headers }

      it "returns user profile with current device info" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Profile retrieved successfully")
        expect(json.dig("data", "user")).to be_present
        expect(json.dig("data", "current_device", "device_id")).to eq(device_registration.device_id)
      end
    end

    context "when missing Authorization token" do
      let(:request_headers) { headers.except("Authorization") }
      it_behaves_like "unauthorized request", "Missing authorization token"
    end

    context "when missing Device-ID header" do
      let(:request_headers) { headers.except("Device-ID") }
      it_behaves_like "unauthorized request", "Missing device ID"
    end

    context "when token is expired" do
      let(:request_headers) { headers }

      before do
        allow_any_instance_of(Auth::TokenService)
          .to receive(:decode_and_verify_token)
          .and_raise(Errors::TokenExpired.new("Token has expired"))
      end

      it "returns token expired error" do
        subject
        expect(response).to have_http_status(:unauthorized)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Token has expired")
      end
    end

    context "when token is invalid" do
      let(:request_headers) { headers }

      before do
        allow_any_instance_of(Auth::TokenService)
          .to receive(:decode_and_verify_token)
          .and_raise(Errors::InvalidToken.new("Invalid token"))
      end

      it_behaves_like "unauthorized request", "Invalid token"
    end
  end

  describe "GET /api/v1/devices" do
    subject { get "/api/v1/devices", headers: headers }

    it "returns a list of active devices with current device marked" do
      create_list(:device_registration, 2, user: user)

      subject
      expect(response).to have_http_status(:ok)
      json = response.parsed_body

      expect(json.dig("status", "message")).to eq("Active devices retrieved successfully")
      expect(json.dig("data", "devices")).to be_an(Array)
      expect(json["data"]["devices"].any? { |d| d["is_current"] }).to be true
    end
  end

  describe "PUT /api/v1/profile/photo" do
    subject { put "/api/v1/profile/photo", params: params, headers: headers }

    let(:photo) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png") }

    context "when photo is provided" do
      let(:params) { { photo: photo } }

      it "updates the user's profile photo and returns success" do
        expect_any_instance_of(User).to receive(:add_photo).with(kind_of(ActionDispatch::Http::UploadedFile))

        subject

        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Profile photo updated successfully")

        expect(json.dig("data")).to include("photo_url")
      end
    end

    context "when photo is missing" do
      let(:params) { {} }

      it "returns unprocessable entity with error message" do
        subject

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Photo is required")
      end
    end
  end

  describe "DELETE /api/v1/profile/photo" do
    subject { delete "/api/v1/profile/photo", headers: headers }

    context "when user has a photo attached" do
      before do
        user.photo.attach(io: File.open(Rails.root.join("spec/fixtures/files/car_1.png")), filename: "test.png", content_type: "image/png")
      end

      it "deletes the profile photo and returns success" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("Profile photo deleted successfully")
      end
    end

    context "when user has no photo attached" do
      it "returns unprocessable entity with error message" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("No photo attached")
      end
    end
  end

  describe "DELETE /api/v1/devices/:device_id" do
    subject { delete "/api/v1/devices/#{target_device_id}", headers: headers }

    let!(:other_device) { create(:device_registration, user: user, active: true, refresh_token_expires_at: 1.day.from_now) }
    let(:target_device_id) { other_device.device_id.to_s }

    let(:valid_token) { Auth::TokenService.new(other_device).generate_tokens[:access_token] }

    let(:headers) do
      {
        "Authorization" => "Bearer #{valid_token}",
        "Device-ID" => target_device_id
      }
    end

    context "when logging out a valid device" do
      before do
        allow_any_instance_of(User).to receive(:logout_device)

        mock_relation = double("ActiveDevicesRelation", find_by: other_device)
        allow(mock_relation).to receive(:find_by).with(device_id: target_device_id).and_return(other_device)

        allow_any_instance_of(User).to receive(:active_devices).and_return(mock_relation)
      end

      it "logs out the device and returns success" do
        expect_any_instance_of(User).to receive(:logout_device).with(target_device_id)

        subject

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("Device logged out successfully")
      end
    end

    context "when device not found" do
      let!(:auth_device) { create(:device_registration, user: user, active: true, refresh_token_expires_at: 1.day.from_now) }
      let(:target_device_id) { "non-existent-id" }

      let(:valid_token) { Auth::TokenService.new(auth_device).generate_tokens[:access_token] }

      let(:headers) do
        {
          "Authorization" => "Bearer #{valid_token}",
          "Device-ID" => auth_device.device_id
        }
      end

      before do
        mock_relation = double("ActiveDevicesRelation", find_by: nil)
        allow(mock_relation).to receive(:find_by).with(device_id: target_device_id).and_return(nil)
        allow_any_instance_of(User).to receive(:active_devices).and_return(mock_relation)
      end

      it "returns 404 device not found" do
        subject
        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body.dig("status", "message")).to eq("Device not found")
      end
    end
  end

  describe "DELETE /api/v1/devices" do
    subject { delete "/api/v1/devices", headers: headers }

    before do
      allow_any_instance_of(User).to receive(:logout_all_devices)
    end

    it "logs out all devices and returns success" do
      expect_any_instance_of(User).to receive(:logout_all_devices)

      subject

      expect(response).to have_http_status(:ok)
      expect(response.parsed_body.dig("status", "message")).to eq("All devices logged out successfully")
    end
  end

  describe "POST /api/v1/auth/setup-2fa" do
    subject { post "/api/v1/auth/setup-2fa", headers: headers }

    let(:mock_setup_result) do
      {
        secret_key: "ABCDEFGHIJKLMNOP",
        qr_code: "data:image/png;base64,QRCodeData",
        qr_code_url: "otpauth://totp/DealerDrive:<EMAIL>?secret=ABCDEFGHIJKLMNOP&issuer=DealerDrive"
      }
    end

    context "when authenticated" do
      before do
        allow_any_instance_of(OtpService).to receive(:setup_totp).and_return(mock_setup_result)
      end

      it "returns 2FA setup information" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("2FA setup initiated successfully")
        expect(json.dig("data", "secret_key")).to eq(mock_setup_result[:secret_key])
        expect(json.dig("data", "qr_code")).to eq(mock_setup_result[:qr_code])
        expect(json.dig("data", "qr_code_url")).to eq(mock_setup_result[:qr_code_url])
      end
    end

    context "when not authenticated" do
      let(:headers) { {} }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Missing authorization token")
      end
    end

    context "when OtpService raises an error" do
      before do
        allow_any_instance_of(OtpService).to receive(:setup_totp).and_raise(Errors::AuthenticationError.new("2FA setup failed: Failed to generate QR code"))
      end

      it "returns error response" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("2FA setup failed: Failed to generate QR code")
      end
    end
  end

  describe "POST /api/v1/auth/verify-2fa-setup" do
    subject { post "/api/v1/auth/verify-2fa-setup", params: params, headers: headers }

    let(:params) { { otp: otp_code } }
    let(:otp_code) { "123456" }
    let(:mock_secret) { "ABCDEFGHIJKLMNOP" }

    before do
      allow(Rails.cache).to receive(:read).with("totp_setup_#{user.id}").and_return(mock_secret)
      mock_totp = instance_double(ROTP::TOTP)
      allow(mock_totp).to receive(:verify).with(otp_code, drift_behind: 15).and_return(verification_result)
      allow(ROTP::TOTP).to receive(:new).with(mock_secret, issuer: OtpService::ISSUER).and_return(mock_totp)
    end

    context "when authenticated and OTP code is valid" do
      let(:verification_result) { Time.now.to_i }

      it "enables 2FA and returns success" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("2FA successfully enabled")

        expect(user.reload.totp_secret).to eq(mock_secret)
        expect(user.preferred_2fa).to eq("totp")
      end
    end

    context "when OTP code is invalid" do
      let(:verification_result) { nil }

      it "returns unprocessable entity" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid OTP code")
      end
    end

    context "when OTP code is missing" do
      let(:params) { {} }
      let(:verification_result) { nil }

      it "returns unprocessable entity" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to eq("OTP code is required")
      end
    end

    context "when setup session expired" do
      before do
        allow(Rails.cache).to receive(:read).with("totp_setup_#{user.id}").and_return(nil)
      end
      let(:verification_result) { nil }

      it "returns unprocessable entity" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to eq("2FA setup session expired. Please start setup again.")
      end
    end
  end

  describe "POST /api/v1/auth/verify-2fa" do
    subject { post "/api/v1/auth/verify-2fa", params: params }

    let(:params) do
      {
        email: user.email,
        otp: otp_code,
        temporary_token: temporary_token,
        device_id: "test-device-123",
        device_os: "iOS",
        app_version: "1.0.0",
        app_build_number: "100"
      }
    end
    let(:otp_code) { "123456" }
    let(:temporary_token) { "valid.temp.token" }
    let(:mock_login_result) { { access_token: "new.access.token", refresh_token: "new.refresh.token", expires_at: 1.hour.from_now.to_i } }

    before do
      allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
      allow(Auth::DeviceLoginService).to receive(:call).and_return(mock_login_result)
    end

    context "when OTP code is valid" do
      before do
        allow_any_instance_of(OtpService).to receive(:validate_otp).and_return(true)
      end

      it "returns new access tokens" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Logged in successfully.")
        expect(json.dig("data", "access_token")).to eq(mock_login_result[:access_token])
        expect(json.dig("data", "refresh_token")).to eq(mock_login_result[:refresh_token])
        expect(json.dig("data", "expires_at")).to eq(mock_login_result[:expires_at])
      end
    end

    context "when OTP code is invalid" do
      before do
        allow_any_instance_of(OtpService).to receive(:validate_otp).and_return(false)
      end

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid OTP")
      end
    end

    context "when temporary token is invalid" do
      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_raise(Errors::InvalidToken.new("Invalid token"))
      end

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid token")
      end
    end

    context "when required params are missing" do
      let(:params) { {} }

      it "returns unprocessable entity" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Token is required")
      end
    end

    context "when device info is missing" do
      let(:params) do
        {
          email: user.email,
          otp: otp_code,
          temporary_token: temporary_token
        }
      end

      it "returns unprocessable entity" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Device ID, app version, device OS and app build number are required for API authentication.")
      end
    end
  end

  describe "POST /api/v1/auth/request-2fa-method" do
    subject { post "/api/v1/auth/request-2fa-method", params: params }

    let(:params) do
      {
        temporary_token: temporary_token,
        method: method
      }
    end
    let(:temporary_token) { "valid.temp.token" }
    let(:method) { "email" }
    let(:mock_expiry) { 5.minutes.from_now }

    before do
      allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
      allow_any_instance_of(OtpService).to receive(:generate_and_send_otp)
      allow_any_instance_of(User).to receive(:masked_destination).and_return("m***@example.com")
      allow_any_instance_of(Auth::TokenService).to receive(:generate_temporary_token)
        .and_return([ temporary_token, mock_expiry ])
    end

    context "when requesting a valid 2FA method" do
      it "sends OTP and returns success" do
        expect_any_instance_of(OtpService).to receive(:generate_and_send_otp).with(method)

        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("2FA challenge sent successfully.")
        expect(json.dig("data", "temporary_token")).to eq(temporary_token)
        expect(json.dig("data", "challenge_type")).to eq(method)
        expect(json.dig("data", "expires_at")).to eq(mock_expiry.as_json)
        expect(json.dig("data", "two_factor_methods")).to contain_exactly("sms", "email")
      end

      context "when user has TOTP configured" do
        before do
          user.update!(totp_secret: "ABCDEFGHIJKLMNOP")
        end

        it "includes totp in available methods" do
          subject
          expect(response).to have_http_status(:ok)
          json = response.parsed_body

          expect(json.dig("data", "two_factor_methods")).to contain_exactly("sms", "email", "totp")
        end
      end
    end

    context "when method is invalid" do
      let(:method) { "invalid" }

      it "returns unprocessable entity" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid 2FA Method. Must be 'sms' or 'email'")
      end
    end

    context "when token is missing" do
      let(:params) { { method: method } }

      it "returns unprocessable entity" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to eq("Temporary Token is required")
      end
    end

    context "when method is missing" do
      let(:params) { { temporary_token: temporary_token } }

      it "returns unprocessable entity" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to eq("2FA Method is required")
      end
    end

    context "when token is invalid" do
      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_raise(Errors::InvalidToken.new("Invalid token"))
      end

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid token")
      end
    end

    context "when token is expired" do
      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_raise(Errors::TokenExpired.new("Token has expired"))
      end

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Token has expired")
      end
    end
  end
end

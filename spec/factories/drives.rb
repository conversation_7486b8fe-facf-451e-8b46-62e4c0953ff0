FactoryBot.define do
  factory :drive do
    dealership
    vehicle { create(:vehicle, dealership: dealership) }
    customer
    sales_person do
      user = create(:user)
      create(:user_dealership,
             user: user,
             dealership: dealership,
             role: :sales_person)
      user
    end
    drive_type { Drive.drive_types.keys.sample }
    status { Drive.statuses.keys.sample }
    notes { Faker::Lorem.paragraph }
    start_datetime { Time.current - 1.hour }
    end_datetime { Time.current }

    trait :with_initial_damage_report do
      after(:create) do |drive|
        create(:damage_report, drive: drive, report_type: DamageReport::INITIAL)
      end
    end

    trait :with_final_damage_report do
      after(:create) do |drive|
        create(:damage_report, drive: drive, report_type: DamageReport::FINAL)
      end
    end

    trait :with_waypoints do
      after(:create) do |drive|
        create_list(:gps_location, 3, trackable: drive)
      end
    end
  end
end

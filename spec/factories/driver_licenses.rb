FactoryBot.define do
  factory :driver_license do
    holder { create(:user, :australian_phone) }
    licence_number { Faker::Number.number(digits: 10).to_s }
    expiry_date { Faker::Date.forward(days: 365) }
    issue_date { Faker::Date.backward(days: 365) }
    category { "A" }
    issuing_country { "au" }
    issuing_state { "NSW" }
    full_name { Faker::Name.name }
    date_of_birth { Faker::Date.birthday(min_age: 18, max_age: 65) }

    trait :with_driving_license_images do
      after(:build) do |driver_license|
        driver_license.front_image.attach(
          io: File.open(Rails.root.join("spec", "fixtures", "files", "front_dl.png")),
          filename: "front.png",
          content_type: "image/png"
        )

        driver_license.back_image.attach(
          io: File.open(Rails.root.join("spec", "fixtures", "files", "back_dl.png")),
          filename: "back.png",
          content_type: "image/png"
        )
      end
    end

    trait :with_invalid_driving_license_images do
      after(:build) do |driver_license|
        driver_license.front_image.attach(
          io: StringIO.new("invalid front image content"),
          filename: "front.txt",
          content_type: "text/plain"
        )

        driver_license.back_image.attach(
          io: StringIO.new("invalid back image content"),
          filename: "back.txt",
          content_type: "text/plain"
        )
      end
    end
  end
end

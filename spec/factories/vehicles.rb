FactoryBot.define do
  factory :vehicle do
    dealership
    status { Vehicle.statuses.keys.sample }
    vin { Faker::Vehicle.vin }
    stock_number { Faker::Alphanumeric.alphanumeric(number: 10).upcase }
    rego { Faker::Vehicle.license_plate }
    make { Faker::Vehicle.make }
    model { Faker::Vehicle.model }
    color { Faker::Vehicle.color }
    year { rand(2018..Date.current.year + 1) }
    rego_expiry { Faker::Date.forward(days: 365) }
    is_trade_plate_used { false }
    last_known_odometer_km { Faker::Number.between(from: 0, to: 200_000) }
    last_known_fuel_gauge_level { Faker::Number.between(from: 0, to: 100) }
    last_system_inspection_timestamp { Faker::Time.backward(days: 14) }

    trait :with_damage_report do
      after(:create) do |vehicle|
        create(:damage_report, vehicle: vehicle)
      end
    end

    trait :with_gps_location do
      after(:create) do |vehicle|
        create(:gps_location, trackable: vehicle)
      end
    end

    trait :with_photos do
      after(:build) do |vehicle|
        vehicle.photos.attach([
          {
            io: File.open(Rails.root.join("spec", "fixtures", "files", "car_1.png")),
            filename: "car_1.png",
            content_type: "image/png"
          },
          {
            io: File.open(Rails.root.join("spec", "fixtures", "files", "car_2.jpg")),
            filename: "car_2.jpg",
            content_type: "image/jpeg"
          }
        ])
      end
    end

    trait :with_invalid_photos do
      after(:build) do |vehicle|
        vehicle.photos.attach(
          {
            io: StringIO.new("some text content"),
            filename: "test.txt",
            content_type: "text/plain"
          }
        )
      end
    end

    trait :with_test_drives do
      after(:create) do |vehicle|
        create_list(:drive, 3, vehicle: vehicle)
      end
    end
  end
end

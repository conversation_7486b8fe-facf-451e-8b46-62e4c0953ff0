# frozen_string_literal: true

module Jwt<PERSON>estHelpers
  def generate_expired_token(user, device_id)
    expiry_time = 1.day.ago.to_i
    payload = {
      sub: user.id,
      scp: "user",
      jti: SecureRandom.uuid,
      device_id: device_id,
      exp: expiry_time,
      token_type: "access",
      issued_at: Time.current.to_i
    }
    JWT.encode(
      payload,
      Rails.application.credentials.devise_jwt_secret_key!,
      'HS256'
    )
  end
end

RSpec.configure do |config|
  config.include JwtTestHelpers, type: :request
end

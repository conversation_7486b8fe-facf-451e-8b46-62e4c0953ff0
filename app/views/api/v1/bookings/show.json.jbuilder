json.booking do
  json.uuid @booking.uuid
  json.drive_type @booking.drive_type
  json.expected_pickup_date_time format_iso8601_with_offset(@booking.expected_pickup_datetime)
  json.expected_return_date_time format_iso8601_with_offset(@booking.expected_return_datetime)
  json.notes @booking.notes

  json.vehicle do
    json.uuid @booking.vehicle.uuid
    json.extract! @booking.vehicle, :make, :model, :year, :vin if @booking.vehicle
  end

  json.customer do
    json.uuid @booking.customer.uuid
    json.extract! @booking.customer, :first_name, :last_name, :email if @booking.customer
  end

  json.dealership do
    json.uuid @booking.dealership.uuid
    json.extract! @booking.dealership, :name if @booking.dealership
  end

  json.sales_person do
    json.uuid @booking.sales_person.uuid
    json.extract! @booking.sales_person, :first_name, :last_name if @booking.sales_person
  end

  json.created_at format_iso8601_with_offset(@booking.created_at)
  json.updated_at format_iso8601_with_offset(@booking.updated_at)
end

class Drive < ApplicationRecord
  include HasUuid

  # Associations
  belongs_to :dealership
  belongs_to :vehicle
  belongs_to :customer
  belongs_to :driver_license, optional: true
  belongs_to :sales_person, class_name: "User"
  has_one :initial_damage_report, -> { where(report_type: DamageReport::INITIAL) }, class_name: "DamageReport", dependent: :destroy
  has_one :final_damage_report, -> { where(report_type: DamageReport::FINAL) }, class_name: "DamageReport", dependent: :destroy
  belongs_to :sales_person_accompanying, class_name: "User", optional: true
  belongs_to :trade_plate, optional: true
  has_many :waypoints, -> { order(created_at: :asc) }, as: :trackable, class_name: "GpsLocation", dependent: :destroy

  validates :end_datetime, comparison: { greater_than: :start_datetime, message: "must be after start time"  },
            if: -> { start_datetime.present? && end_datetime.present? }
  validates :expected_return_datetime, comparison: { greater_than: :expected_pickup_datetime, message: "must be after pickup time" },
            if: -> { expected_pickup_datetime.present? && expected_return_datetime.present? }
  validate :sales_person_belongs_to_dealership

  enum :status, {
    scheduled: 0,
    in_progress: 1,
    completed: 2,
    cancelled: 3
  }, default: :scheduled

  enum :drive_type, {
    test_drive: 0,
    enquiry: 1,
    loan: 2,
    appraisal: 3,
    loan_booking: 4,
    test_drive_booking: 5,
    self_loan: 6
  }, default: :test_drive

  enum :sold_status, {
    not_sold: 0,
    sold: 1
  }, default: :not_sold

  scope :active, -> { where(status: :in_progress) }

  validate :validate_timestamps_not_in_future
  validate :validate_odometer_readings

  private

  def validate_timestamps_not_in_future
    now = Time.current

    if start_datetime.present? && start_datetime > now
      errors.add(:start_datetime, "cannot be in the future")
    end

    if end_datetime.present? && end_datetime > now
      errors.add(:end_datetime, "cannot be in the future")
    end
  end

  def validate_odometer_readings
    validate_odometer_positive_values
    validate_odometer_sequence
  end

  def validate_odometer_positive_values
    if start_odometer_reading.present? && start_odometer_reading.negative?
      errors.add(:start_odometer_reading, "must be a positive number")
    end

    if end_odometer_reading.present? && end_odometer_reading.negative?
      errors.add(:end_odometer_reading, "must be a positive number")
    end
  end

  def validate_odometer_sequence
    return unless start_odometer_reading.present? && end_odometer_reading.present?

    if start_odometer_reading > end_odometer_reading
      errors.add(:end_odometer_reading, :invalid_sequence, message: "must be greater than start odometer reading")
    end
  end

  def sales_person_belongs_to_dealership
    return unless sales_person && dealership

    unless sales_person.dealerships.include?(dealership)
      errors.add(:sales_person, "must belong to the vehicle's dealership")
    end
  end
end

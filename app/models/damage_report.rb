class DamageReport < ApplicationRecord
  include HasUuid

  INITIAL = 0
  FINAL = 1
  VEHICLE = 3

  belongs_to :drive, optional: true
  belongs_to :vehicle

  has_many_attached :media_files

  enum :report_type, {
    initial: INITIAL,
    final: FINAL,
    vehicle: VEHICLE
  }

  validate :report_type_present_for_drive
  validate :validate_media_files_size_and_type
  validates :media_files, content_type: [ "image/png", "image/jpeg", "video/mp4", "video/quicktime" ],
                   limit: { max: 5 }

  private

  def validate_media_files_size_and_type
    return unless media_files.attached?

    media_files.each do |file|
      validate_media_file_size(file)
    end
  end

  def validate_media_file_size(file)
    if file.image?
      if file.byte_size > 5.megabytes
        errors.add(:media_files, "size should not exceed 5MB for images")
      end
    elsif file.video?
      if file.byte_size > 20.megabytes
        errors.add(:media_files, "size should not exceed 20MB for videos")
      end
    else
      errors.add(:media_files, "must be an image or video")
    end
  end

  def report_type_present_for_drive
    if drive_id.present? && report_type.blank?
      errors.add(:report_type, "is needed to save damage report for drive")
    end
  end
end

class Api::V1::Dealerships::VehiclesController < Api::V1::BaseController
  before_action :validate_type_filter, only: [ :index ]

  def index
    @pagy, @vehicles = pagy(
      filtered_vehicles,
      items: pagination_validated_per_page,
      page: permitted_params[:page] || 1
    )

    set_pagination_headers(@pagy)

    render_success_response("Dealership vehicles retrieved successfully", {
      vehicles: Api::V1::VehicleSerializer.serialize_collection(@vehicles)
    })
  end

  private

  def filtered_vehicles
    dealership_vehicles = dealership.vehicles.not_deleted.ordered_by_name

    if @vehicle_type.present?
      dealership_vehicles.where(vehicle_type: @vehicle_type)
    else
      dealership_vehicles
    end
  end

  def validate_type_filter
    @vehicle_type = permitted_params[:vehicle_type]
    return unless @vehicle_type.present?

    unless Vehicle.vehicle_types.key?(@vehicle_type)
      raise Errors::InvalidInput, "Invalid vehicle type filter. Valid filters: #{Vehicle.vehicle_types.keys.join(', ')}"
    end
  end

  def permitted_params
    params.permit(:dealership_uuid, :page, :per_page, :vehicle_type)
  end
end

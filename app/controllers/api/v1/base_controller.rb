# frozen_string_literal: true

class Api::V1::BaseController < ApplicationController
  include JwtAuthenticatable
  include ResponseHandler
  include DateTimeFormatHelper
  include ActiveStorage::SetCurrent
  include Pagy::Backend

  private

  def set_pagination_headers(pagy)
    response.headers["X-Current-Page"] = pagy.page.to_s
    response.headers["X-Per-Page"] = pagy.items.to_s
    response.headers["X-Total-Count"] = pagy.count.to_s
    response.headers["X-Total-Pages"] = pagy.pages.to_s
    response.headers["X-Next-Page"] = pagy.next.to_s if pagy.next
    response.headers["X-Prev-Page"] = pagy.prev.to_s if pagy.prev
  end


  def pagination_validated_per_page
    per_page = params[:per_page].to_i

    # Return default if per_page is 0 or negative
    return Pagy::DEFAULT[:items] if per_page <= 0

    # Enforce max_items limit
    [ per_page, Pagy::DEFAULT[:max_items] ].min
  end

  private

  def dealership
    @dealership ||= current_user.dealerships.find_by!(uuid: params[:dealership_uuid])
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Dealership not found or you don't have access to it"
  end
end

# frozen_string_literal: true

class Api::V1::BookingsController < Api::V1::BaseController
  def create
    validate_drive_type!

    # Create drive record
    @booking = Drive.new(
      dealership_id: dealership.id,
      vehicle_id: vehicle.id,
      drive_type: booking_params[:drive_type],
      customer: customer,
      expected_pickup_datetime: parse_date_time(booking_params[:expected_pickup_date_time]),
      expected_return_datetime: parse_date_time(booking_params[:expected_return_date_time]),
      sales_person_id: sales_person.id,
      notes: booking_params[:notes]
    )

    @booking.save!
    render :show, status: :created
  end

  private

  def validate_drive_type!
    unless %w[ test_drive_booking loan_booking ].include?(booking_params[:drive_type])
      raise Errors::InvalidInput, "Invalid drive_type. Must be either 'test_drive_booking' or 'loan_booking'"
    end
  end

  def booking_params
    params.permit(
      :dealership_uuid,
      :vehicle_uuid,
      :drive_type,
      :expected_pickup_date_time,
      :expected_return_date_time,
      :sales_person_uuid,
      :customer_uuid,
      :notes,
      customer_info: [
        :first_name,
        :last_name,
        :age,
        :email,
        :phone_number,
        :gender,
        :address_line1,
        :address_line2,
        :suburb,
        :city,
        :state,
        :country,
        :postcode,
        :company_name,
        driver_license: [
          :licence_number,
          :expiry_date,
          :issuing_state,
          :issuing_country,
          :full_name,
          :date_of_birth
        ]
      ]
    )
  end

  def customer
    @customer ||=
      if booking_params[:customer_uuid].present?
        dealership.customers.find_by!(uuid: booking_params[:customer_uuid])
      else
        customer_attributes = booking_params[:customer_info].to_h
        if customer_attributes[:driver_license].present?
          customer_attributes[:driver_license_attributes] = customer_attributes.delete(:driver_license)
        end
        dealership.customers.new(customer_attributes)
      end
  end

  def driver_license
    @driver_license ||= customer.driver_license
  end

  def vehicle
    @vehicle ||= dealership.vehicles.find_by!(uuid: booking_params[:vehicle_uuid])
  rescue ActiveRecord::RecordNotFound
      raise Errors::RecordNotFound, "Vehicle not found"
  end

  def sales_person
    if booking_params[:sales_person_uuid].blank?
      @sales_person ||= current_user
    else
      @sales_person ||= dealership.sales_people.find_by!(uuid: booking_params[:sales_person_uuid])
    end
  rescue ActiveRecord::RecordNotFound
      raise Errors::RecordNotFound, "Sales person not found"
  end
end

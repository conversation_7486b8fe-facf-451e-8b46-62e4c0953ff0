class Api::V1::DeviceSerializer < Api::V1::BaseSerializer
  set_id :uuid
  set_type :device_registration

  attributes :uuid, :device_id, :fcm_token, :device_name, :device_os, :device_os_version,
             :app_version, :app_build_number, :last_login_timestamp, :last_activity_at, :is_current

  attribute :is_current do |device_registration, params|
    device_registration.id == params[:current_device]&.id
  end
end

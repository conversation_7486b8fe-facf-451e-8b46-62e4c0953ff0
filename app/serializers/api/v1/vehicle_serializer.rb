class Api::V1::VehicleSerializer < Api::V1::BaseSerializer
  set_id :uuid
  set_type :vehicle
  attributes :uuid, :make, :model, :year, :rego, :vin, :stock_number, :color, :vehicle_type, :status, :last_known_odometer_km,
             :last_known_fuel_gauge_level, :display_name, :created_at, :updated_at, :last_system_inspection_timestamp,
             :rego_expiry, :is_trade_plate_used, :available_for_drive

  class << self
    def serialize_collection(vehicles)
      new(vehicles).serializable_hash[:data].map { |item| item[:attributes] }
    end
  end
end

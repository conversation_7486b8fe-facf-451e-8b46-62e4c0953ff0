class Api::V1::UserSerializer < Api::V1::BaseSerializer
  # Use :uuid as the ID key
  set_id :uuid
  set_type :user

  attributes :uuid, :email, :name, :first_name, :last_name, :phone, :preferred_2fa,
             :two_factor_methods, :profile_photo_url, :user_type, :job_title,
             :password_change_required, :preferred_language, :external_id, :time_zone,
             :onboarding_completed, :dealership_role

  has_many :dealerships

  attribute :two_factor_methods do |user|
    user.available_2fa_methods
  end

  attribute :dealership_role do |user, params|
    user.dealership_role(params[:dealership]) if params && params[:dealership]
  end

  class << self
    def serialize_collection(users, params = {})
      new(users, { params: params }).serializable_hash[:data].map { |item| item[:attributes] }
    end
  end
end

class Api::V1::DealershipSerializer < Api::V1::BaseSerializer
  set_id :uuid
  set_type :dealership

  attributes :uuid, :name, :long_name, :setting_date_format, :setting_time_zone, :setting_distance_unit, :created_at, :updated_at,
             :status, :address_line1, :address_line2, :suburb, :state, :postcode, :country, :phone, :email, :website, :external_id,
             :abn

  class << self
    def serialize_collection(dealerships)
      new(dealerships).serializable_hash[:data].map { |item| item[:attributes] }
    end
  end
end

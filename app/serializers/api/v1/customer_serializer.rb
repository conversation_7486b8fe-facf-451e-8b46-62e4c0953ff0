class Api::V1::CustomerSerializer < Api::V1::BaseSerializer
  set_id :uuid
  set_type :customer

  attributes :uuid, :first_name, :last_name, :full_name, :email, :phone_number,
             :age, :gender, :company_name, :external_id, :postcode, :suburb,
             :address_line1, :address_line2, :city, :state, :country,
             :created_at, :updated_at

  belongs_to :dealership

  class << self
    def serialize_collection(customers)
      new(customers).serializable_hash[:data].map { |item| item[:attributes] }
    end
  end
end

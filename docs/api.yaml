openapi: 3.0.3
info:
  title: Dealerdrive APIs
  description: API for dealerdrive app
  version: 1.0.0
  contact:
    {
      "name": "Carsales Aus",
      "url": "https://carsales.com.au",
      "email": "<EMAIL>",
    }
servers:
  - url: "https://api.dealerdrive.com.au/v1"
    description: Production server
  - url: "https://devapi.dealerdrive.com.au/v1"
    description: Development server
tags:
  - name: Authentication
    description: Authentication operations
  - name: Users
    description: user operations
  - name: Profile
    description: profile operations
  - name: Devices
    description: devices operations
  - name: appraisals
    description: Operations for creating and managing appraisals
  - name: customers
    description: Customer management operations
  - name: dealerships
    description: Dealership management operations
  - name: images
    description: Image upload and management
  - name: trade_plates
    description: Trade plate management operations
paths:
  /auth/login:
    post:
      summary: First step of authentication - validate credentials and trigger 2FA
      operationId: initiateLogin
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserLogin"
      responses:
        "200":
          description: "Credentials valid, 2FA challenge sent"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TwoFactorChallengeResponse"
        "401":
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "403":
          description: Account locked or other security issue
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /auth/verify-2fa:
    post:
      summary: Second step of authentication - verify OTP and complete login
      operationId: verifyTwoFactor
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TwoFactorVerificationRequest"
      responses:
        "200":
          description: "2FA verified, login successful"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuthResponse"
        "401":
          description: Invalid or expired OTP
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /auth/resend-otp:
    post:
      summary: Resend OTP code for verification
      operationId: resentOTP
      description: |
        Resends the OTP via SMS or Email based on the intermediate token from the login step.
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ResendOTPRequest"
      responses:
        "200":
          description: OTP successfully sent
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResendOTPResponse"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Invalid intermediate token
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /auth/request-2fa-method:
    post:
      summary: Request a different 2FA method (SMS/Email)
      operationId: requestAlternativeTwoFactor
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - temporaryToken
                - method
              properties:
                temporaryToken:
                  type: string
                  description: Temporary token from initial login
                method:
                  type: string
                  enum:
                    - sms
                    - email
                  description: Preferred 2FA delivery method
      responses:
        "200":
          description: New 2FA challenge sent
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TwoFactorChallengeResponse"
        "401":
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /auth/setup-2fa:
    post:
      summary: Setup authenticator app for 2FA
      operationId: setupTwoFactorApp
      tags:
        - Authentication
      security:
        - BearerAuth: []
      responses:
        "200":
          description: 2FA setup initiated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TwoFactorSetupResponse"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /auth/verify-2fa-setup:
    post:
      summary: Verify authenticator app setup
      operationId: verifyTwoFactorSetup
      tags:
        - Authentication
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - otpCode
              properties:
                otpCode:
                  type: string
                  description: OTP code from authenticator app
                  example: "123456"
      responses:
        "200":
          description: 2FA setup verified and enabled
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 2FA successfully enabled
        "401":
          description: Unauthorized or invalid OTP
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /auth/refresh:
    post:
      summary: Get new access token using refresh token
      operationId: refreshToken
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refreshToken
              properties:
                refreshToken:
                  type: string
                  description: Refresh token received during login
      responses:
        "200":
          description: New tokens generated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuthResponse"
        "401":
          description: Invalid or expired refresh token
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /users/me:
    get:
      summary: Get current user profile
      operationId: getCurrentUser
      tags:
        - Users
      security:
        - BearerAuth: []
      responses:
        "200":
          description: User profile retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserProfile"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /profile/photo:
    post:
      summary: Upload profile photo
      operationId: updateProfilePhoto
      description: Add or update the user's profile photo
      security:
        - BearerAuth: []
      tags:
        - Profile
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                photo:
                  type: string
                  format: binary
                  description: The profile photo to upload
      responses:
        "200":
          description: Profile photo uploaded successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProfilePhotoResponse"
        "400":
          description: Invalid request or image format
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    delete:
      summary: Remove profile photo
      operationId: removeProfilePhoto
      description: Delete the user's profile photo
      security:
        - BearerAuth: []
      tags:
        - Profile
      responses:
        "200":
          description: Profile photo removed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProfilePhotoResponse"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Profile photo not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /users/me/documents/driving-license:
    get:
      summary: Get user's driving license information
      operationId: getDrivingLicense
      tags:
        - User Documents
      security:
        - BearerAuth: []
      responses:
        "200":
          description: Driving license information retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DrivingLicense"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Driving license not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    post:
      summary: Add or update user's driving license
      operationId: addOrUpdateDrivingLicense
      tags:
        - User Documents
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DrivingLicenseInput"
      responses:
        "200":
          description: Driving license updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DrivingLicense"
        "201":
          description: Driving license added successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DrivingLicense"
        "400":
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    delete:
      summary: Remove user's driving license
      operationId: removeDrivingLicense
      tags:
        - User Documents
      security:
        - BearerAuth: []
      responses:
        "200":
          description: Driving license removed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Driving license removed successfully
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Driving license not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /users/me/documents/driving-license-image:
    post:
      summary: Upload license photo
      operationId: uploadLicensePhoto
      description: Add or update the user's profile photo
      security:
        - BearerAuth: []
      tags:
        - User Documents
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                front:
                  type: string
                  format: binary
                  description: The profile photo to upload
                back:
                  type: string
                  format: binary
                  description: The profile photo to upload
      responses:
        "200":
          description: License photo uploaded successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DrivingLicense"
        "400":
          description: Invalid request or image format
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    delete:
      summary: Remove Driving license photo
      operationId: removeLicensePhoto
      description: Delete the user's Driving license photo
      security:
        - BearerAuth: []
      tags:
        - User Documents
      responses:
        "200":
          description: Driving license photo removed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DrivingLicense"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Driving license photo not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /devices:
    post:
      summary: Register or update device information
      operationId: registerDevice
      tags:
        - Devices
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DeviceRegistration"
      responses:
        "200":
          description: Device registered successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeviceResponse"
        "400":
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    get:
      summary: Get all registered devices for current user
      operationId: getUserDevices
      tags:
        - Devices
      security:
        - BearerAuth: []
      responses:
        "200":
          description: List of devices retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  devices:
                    type: array
                    items:
                      $ref: "#/components/schemas/DeviceResponse"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /auth/forgot-password:
    post:
      summary: Request password reset
      operationId: forgotPassword
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
                  description: User's email address
                  example: <EMAIL>
      responses:
        "200":
          description: Password reset initiated
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Password reset instructions sent to email
                  temporaryToken:
                    type: string
                    description: Temporary token for validating password reset OTP
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                  expiresIn:
                    type: integer
                    description: Token expiration time in seconds
                    example: 900
        "400":
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Email not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /auth/verify-reset-code:
    post:
      summary: Verify reset code from email
      operationId: verifyResetCode
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - temporaryToken
                - resetCode
              properties:
                temporaryToken:
                  type: string
                  description: Temporary token from forgot-password endpoint
                  example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                resetCode:
                  type: string
                  description: Code sent to user's email
                  example: "123456"
      responses:
        "200":
          description: Reset code validated
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  resetToken:
                    type: string
                    description: Token for password reset
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                  expiresIn:
                    type: integer
                    description: Token expiration time in seconds
                    example: 900
        "400":
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Invalid or expired token/code
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /auth/reset-password:
    post:
      summary: Reset password with token
      operationId: resetPassword
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - resetToken
                - newPassword
              properties:
                resetToken:
                  type: string
                  description: Reset token from verify-reset-code endpoint
                  example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                newPassword:
                  type: string
                  format: password
                  description: New password
                  example: newSecurePassword123
      responses:
        "200":
          description: Password reset successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Password reset successful
        "400":
          description: Invalid input or password complexity requirements not met
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Invalid or expired reset token
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /auth/change-password:
    post:
      summary: Change password for authenticated user
      operationId: changePassword
      tags:
        - Authentication
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - currentPassword
                - newPassword
              properties:
                currentPassword:
                  type: string
                  format: password
                  description: Current password
                  example: currentSecurePassword123
                newPassword:
                  type: string
                  format: password
                  description: New password
                  example: newSecurePassword123
      responses:
        "200":
          description: Password changed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Password changed successfully
        "400":
          description: Invalid input or password complexity requirements not met
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized or incorrect current password
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /dealerships:
    get:
      summary: Get list of dealerships for current user
      description: Retrieves all dealerships associated with the logged-in salesperson
      operationId: listDealerships
      security:
        - BearerAuth: []
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Dealership"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/dealerships/{dealershipId}/vehicles":
    get:
      summary: Get vehicles for a specific dealership
      description: Retrieves all vehicles available for drive at the specified dealership
      operationId: listDealershipVehicles
      security:
        - BearerAuth: []
      parameters:
        - name: dealershipId
          in: path
          description: ID of the dealership
          required: true
          schema:
            type: string
            format: uuid
        - name: type
          in: query
          description: Type of car
          required: true
          schema:
            type: string
            enum:
              - new
              - old
              - demo
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Vehicle"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Dealership not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/dealerships/{dealershipId}/users":
    get:
      summary: Get users for a specific dealership
      description: Retrieves all users available at the specified dealership
      operationId: listDealershipUsers
      security:
        - BearerAuth: []
      parameters:
        - name: dealershipId
          in: path
          description: ID of the dealership
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/UserProfile"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Dealership not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/dealerships/{dealershipId}/trade-plates":
    get:
      summary: Get trade plates for a specific dealership
      description: Retrieves all trade plates for the specified dealership with flags indicating expiry and usage status
      operationId: listDealershipTradePlates
      security:
        - BearerAuth: []
      tags:
        - trade_plates
      parameters:
        - name: dealershipId
          in: path
          description: UUID of the dealership
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: "Trade plates retrieved successfully"
                    required:
                      - code
                      - message
                  data:
                    type: object
                    properties:
                      trade_plates:
                        type: array
                        items:
                          $ref: "#/components/schemas/TradePlate"
                    required:
                      - trade_plates
                required:
                  - status
                  - data
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Dealership not found or access denied
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /customers:
    post:
      summary: Create a new customer
      description: Creates a new customer record in the system
      operationId: createCustomer
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Customer"
          multipart/form-data:
            schema:
              type: object
              properties:
                customerData:
                  type: string
                  format: binary
                  description: JSON data of Customer
                drivingLicenseFront:
                  type: string
                  format: binary
                  description: Front image of driving license
                drivingLicenseBack:
                  type: string
                  format: binary
                  description: Back image of driving license
      responses:
        "201":
          description: Customer created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Customer"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    get:
      summary: Search for customers
      description: Search for customers by various criteria
      operationId: searchCustomers
      security:
        - BearerAuth: []
      parameters:
        - name: dealershipId
          in: query
          required: true
          schema:
            type: string
            format: uuid
        - name: q
          in: query
          description: "Search query (name, email, phone, license number)"
          required: false
          schema:
            type: string
        - name: email
          in: query
          description: Filter by exact email match
          required: false
          schema:
            type: string
            format: email
        - name: phone
          in: query
          description: Filter by phone number
          required: false
          schema:
            type: string
        - name: licenseNumber
          in: query
          description: Filter by driving license number
          required: false
          schema:
            type: string
        - name: limit
          in: query
          description: Maximum number of results to return
          required: false
          schema:
            type: integer
            default: 20
            maximum: 100
        - name: offset
          in: query
          description: Number of results to skip (for pagination)
          required: false
          schema:
            type: integer
            default: 0
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: integer
                    description: Total number of matching customers
                  customers:
                    type: array
                    items:
                      $ref: "#/components/schemas/Customer"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/customers/{customerId}":
    get:
      summary: Get customer details
      description: Retrieves detailed information about a specific customer
      operationId: getCustomer
      security:
        - BearerAuth: []
      parameters:
        - name: customerId
          in: path
          description: ID of the customer
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Customer"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    put:
      summary: Update customer details
      description: Updates an existing customer's information
      operationId: updateCustomer
      security:
        - BearerAuth: []
      parameters:
        - name: customerId
          in: path
          description: ID of the customer to update
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Customer"
          multipart/form-data:
            schema:
              type: object
              properties:
                customerData:
                  type: string
                  format: binary
                  description: JSON data of Customer
                drivingLicenseFront:
                  type: string
                  format: binary
                  description: Front image of driving license
                drivingLicenseBack:
                  type: string
                  format: binary
                  description: Back image of driving license
      responses:
        "200":
          description: Customer updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Customer"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /bookings:
    post:
      summary: Initialize a new booking
      description: Creates a new booking
      operationId: initializeBooking
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BookingInitRequest"
      responses:
        "201":
          description: Booking initialized successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Booking"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    get:
      summary: Get list of bookings
      description: Retrieves all bookings for the current user with optional filtering
      operationId: listBookings
      security:
        - BearerAuth: []
      parameters:
        - name: status
          in: query
          description: Filter by booking status
          required: false
          schema:
            type: string
            enum:
              - scheduled
              - completed
              - canceled
        - name: driveType
          in: query
          description: Filter by action type
          required: false
          schema:
            type: string
            enum:
              - test-drive
              - loan
        - name: query
          in: query
          description: Filter by query string
          required: false
          schema:
            type: string
        - name: overdue
          in: query
          description: Filter by action type
          required: false
          schema:
            type: boolean
        - name: dealershipId
          in: query
          description: Filter by dealership ID
          required: false
          schema:
            type: string
            format: uuid
        - name: startDate
          in: query
          description: Filter by start date (inclusive)
          required: false
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: Filter by end date (inclusive)
          required: false
          schema:
            type: string
            format: date
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Booking"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/bookings/{bookingId}":
    get:
      summary: Get booking details
      description: Retrieves detailed information about a specific booking
      operationId: getBooking
      security:
        - BearerAuth: []
      parameters:
        - name: bookingId
          in: path
          description: ID of the booking
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Booking"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    put:
      summary: Update booking details
      description: Updates an existing booking
      operationId: updateBooking
      security:
        - BearerAuth: []
      parameters:
        - name: bookingId
          in: path
          description: ID of the booking to update
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BookingUpdateRequest"
      responses:
        "200":
          description: booking updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Booking"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Booking not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/bookings/{bookingId}/cancel":
    post:
      summary: Cancel a booking
      description: Cancels a booking
      operationId: cancelBooking
      security:
        - BearerAuth: []
      parameters:
        - name: bookingId
          in: path
          description: ID of the booking to cancel
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: Reason for cancellation
      responses:
        "200":
          description: Booking canceled successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Booking"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Booking not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /drives:
    post:
      summary: Initialize a new drive
      description: Creates a new drive in draft status with initial dealership selection
      operationId: initializeDrive
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveInitRequest"
      responses:
        "201":
          description: Drive initialized successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    get:
      summary: Get list of drives
      description: Retrieves all drives for the current user with optional filtering
      operationId: listDrives
      security:
        - BearerAuth: []
      parameters:
        - name: status
          in: query
          description: Filter by drive status
          required: false
          schema:
            type: string
            enum:
              - draft
              - scheduled
              - active
              - completed
              - canceled
        - name: driveType
          in: query
          description: Filter by action type
          required: false
          schema:
            type: string
            enum:
              - test-drive
              - enquiry
              - self-loan
              - loan
              - sold
        - name: query
          in: query
          description: Filter by query string
          required: false
          schema:
            type: string
        - name: overdue
          in: query
          description: Filter by action type
          required: false
          schema:
            type: boolean
        - name: step
          in: query
          description: Filter by current step in the process
          required: false
          schema:
            type: string
            enum:
              - dealership_selection
              - vehicle_selection
              - customer_selection
              - return_time
              - initial_damage
              - odometer_reading
              - agreement
              - start_drive
              - end_drive
              - completed
        - name: dealershipId
          in: query
          description: Filter by dealership ID
          required: false
          schema:
            type: string
            format: uuid
        - name: startDate
          in: query
          description: Filter by start date (inclusive)
          required: false
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: Filter by end date (inclusive)
          required: false
          schema:
            type: string
            format: date
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Drive"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
      x-stoplight:
        id: 4uld7wq7nqm7z
  "/drives/{driveId}/customer":
    put:
      summary: Update drive customer
      description: Sets or updates the customer for a drive
      operationId: updateDriveCustomer
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveCustomerUpdateRequest"
          multipart/form-data:
            schema:
              type: object
              properties:
                customerData:
                  type: string
                  format: binary
                  description: JSON data for customer update
                drivingLicenseFront:
                  type: string
                  format: binary
                  description: Front image of driving license (only required if creating new customer)
                drivingLicenseBack:
                  type: string
                  format: binary
                  description: Back image of driving license (only required if creating new customer)
      responses:
        "200":
          description: Customer updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
      x-stoplight:
        id: jo6bkd150b9kd
  "/drives/{driveId}/return-time":
    put:
      summary: Update drive return time
      description: Sets or updates the expected return time for a drive
      operationId: updateDriveReturnTime
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveReturnTimeUpdateRequest"
      responses:
        "200":
          description: Return time updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
      x-stoplight:
        id: w94hcc5htnov5
  "/drives/{driveId}/initial-damage":
    put:
      summary: Update drive initial damage report
      description: Sets or updates the initial damage report for a drive
      operationId: updateDriveInitialDamage
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveDamageUpdateRequest"
          multipart/form-data:
            schema:
              type: object
              properties:
                damageData:
                  type: string
                  format: binary
                  description: JSON data for damage update
                damagePhotos:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Photos of damaged areas
      responses:
        "200":
          description: Damage report updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
      x-stoplight:
        id: qmip7knz1uux7
  "/drives/{driveId}/odometer":
    put:
      summary: Update drive odometer reading
      description: Sets or updates the starting odometer reading for a drive
      operationId: updateDriveOdometer
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveOdometerUpdateRequest"
      responses:
        "200":
          description: Odometer reading updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
      x-stoplight:
        id: 0lx5o8fwb065z
  "/drives/{driveId}/agreement":
    put:
      summary: Update drive agreement acceptance
      description: Sets or updates the agreement acceptance and customer signature
      operationId: updateDriveAgreement
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveAgreementUpdateRequest"
          multipart/form-data:
            schema:
              type: object
              properties:
                agreementData:
                  type: string
                  format: binary
                  description: JSON data for agreement update
                customerSignature:
                  type: string
                  format: binary
                  description: Customer's signature image
      responses:
        "200":
          description: Agreement updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/drives/{driveId}/start":
    post:
      summary: Start the drive
      description: Transitions the drive from scheduled to active state
      operationId: startDrive
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveStartRequest"
      responses:
        "200":
          description: Drive started successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "422":
          description: Drive cannot be started (missing required information)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/drives/{driveId}/location":
    post:
      summary: Update drive GPS location
      description: Records GPS location during an active drive
      operationId: updateDriveLocation
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GPSLocation"
      responses:
        "200":
          description: Location updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/drives/{driveId}/final-damage":
    put:
      summary: Update drive final damage report
      description: Sets or updates the final damage report for a drive
      operationId: updateDriveFinalDamage
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveDamageUpdateRequest"
          multipart/form-data:
            schema:
              type: object
              properties:
                damageData:
                  type: string
                  format: binary
                  description: JSON data for damage update
                damagePhotos:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Photos of damaged areas
      responses:
        "200":
          description: Final damage report updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/drives/{driveId}/end-odometer":
    put:
      summary: Update drive end odometer reading
      description: Sets or updates the ending odometer reading for a drive
      operationId: updateDriveEndOdometer
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveOdometerUpdateRequest"
      responses:
        "200":
          description: End odometer reading updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/drives/{driveId}/reassign":
    put:
      summary: Reassign drive
      description: Updates user assigned to the drive
      operationId: reassignDrive
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveReassignRequest"
      responses:
        "200":
          description: Reassigned successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/drives/{driveId}/sold":
    put:
      summary: Mark drive sold
      description: Mark the drive sold
      operationId: markSold
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveMarkSoldRequest"
      responses:
        "200":
          description: Marked sold successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/drives/{driveId}/complete":
    post:
      summary: Complete a drive
      description: Marks a drive as completed and records final details
      operationId: completeDrive
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive to complete
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DriveCompletionRequest"
          multipart/form-data:
            schema:
              type: object
              properties:
                completionData:
                  type: string
                  format: binary
                  description: JSON data of DriveCompletionRequest
                finalDamagePhotos:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Photos of any final damage
      responses:
        "200":
          description: Drive completed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/drives/{driveId}/cancel":
    post:
      summary: Cancel a drive
      description: "Cancels a draft, scheduled, or active drive"
      operationId: cancelDrive
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive to cancel
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: Reason for cancellation
      responses:
        "200":
          description: Drive canceled successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  "/drives/{driveId}":
    get:
      summary: Get drive details
      description: Retrieves detailed information about a specific drive
      operationId: getDrive
      security:
        - BearerAuth: []
      parameters:
        - name: driveId
          in: path
          description: ID of the drive
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Drive"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "404":
          description: Drive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /agreements/drive:
    get:
      summary: Get drive agreement template
      description: Retrieves the current drive agreement template text
      operationId: getDriveAgreement
      security:
        - BearerAuth: []
      parameters:
        - name: dealershipId
          in: query
          description: ID of the dealership (for customized agreements)
          required: false
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  agreementText:
                    type: string
                    description: The text of the agreement
                  version:
                    type: string
                    description: Version number of the agreement
                  lastUpdated:
                    type: string
                    format: date
                    description: Date when agreement was last updated
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /appraisals:
    get:
      tags:
        - appraisals
      summary: List appraisals
      operationId: listAppraisals
      security:
        - BearerAuth: []
      parameters:
        - name: dealershipId
          in: query
          required: true
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          schema:
            type: string
            enum:
              - COMPLETED
              - AWARDED
              - INCOMPLETE
              - ARCHIVED
              - DELETED
        - name: favorite
          in: query
          schema:
            type: boolean
        - name: onlyMine
          in: query
          schema:
            type: boolean
        - name: customerQuery
          in: query
          schema:
            type: string
        - name: brand
          in: query
          schema:
            type: string
        - name: model
          in: query
          schema:
            type: string
        - name: registrationNumber
          in: query
          schema:
            type: string
        - name: salesPersonId
          in: query
          schema:
            type: string
        - name: startDate
          in: query
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          schema:
            type: string
            format: date
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: sortBy
          in: query
          schema:
            type: string
            enum:
              - DATE_CREATED
              - BRAND
              - MODEL
              - REGISTRATION
              - CUSTOMER_NAME
            default: DATE_CREATED
        - name: sortOrder
          in: query
          schema:
            type: string
            enum:
              - ASC
              - DESC
            default: DESC
      responses:
        "200":
          description: List of appraisals
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
                  appraisals:
                    type: array
                    items:
                      $ref: "#/components/schemas/AppraisalSummary"
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
    post:
      tags:
        - appraisals
      summary: Create a new appraisal
      operationId: createAppraisal
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AppraisalCreate"
      responses:
        "201":
          description: Appraisal created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Appraisal"
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "403":
          description: Forbidden

  /appraisals/{appraisalId}:
    get:
      tags:
        - appraisals
      summary: Get appraisal details
      operationId: getAppraisalById
      security:
        - BearerAuth: []
      parameters:
        - name: appraisalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Appraisal details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Appraisal"
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Appraisal not found

    delete:
      tags:
        - appraisals
      summary: Delete an appraisal (mark as deleted)
      operationId: deleteAppraisal
      security:
        - BearerAuth: []
      parameters:
        - name: appraisalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "204":
          description: Appraisal marked as deleted
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Appraisal not found

  /appraisals/{appraisalId}/customer:
    put:
      tags:
        - appraisals
      summary: Update just the customer section of an appraisal
      operationId: updateAppraisalCustomer
      security:
        - BearerAuth: []
      parameters:
        - name: appraisalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - type: object
                  required:
                    - customerId
                  properties:
                    customerId:
                      type: string
                      format: uuid
                - $ref: "#/components/schemas/Customer"
      responses:
        "200":
          description: Customer section updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Appraisal"
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Appraisal not found

  /appraisals/{appraisalId}/vehicle:
    put:
      tags:
        - appraisals
      summary: Update just the vehicle section of an appraisal
      operationId: updateAppraisalVehicle
      security:
        - BearerAuth: []
      parameters:
        - name: appraisalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerVehicleDetails"
      responses:
        "200":
          description: Vehicle section updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Appraisal"
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Appraisal not found

  /appraisals/{appraisalId}/finance:
    put:
      tags:
        - appraisals
      summary: Update just the finance section of an appraisal
      operationId: updateAppraisalFinance
      security:
        - BearerAuth: []
      parameters:
        - name: appraisalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FinanceDetails"
      responses:
        "200":
          description: Finance section updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Appraisal"
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Appraisal not found

  /appraisals/{appraisalId}/options:
    put:
      tags:
        - appraisals
      summary: Update just the options section of an appraisal
      operationId: updateAppraisalOptions
      security:
        - BearerAuth: []
      parameters:
        - name: appraisalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OptionsFitted"
      responses:
        "200":
          description: Options section updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Appraisal"
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Appraisal not found

  /appraisals/{appraisalId}/history:
    put:
      tags:
        - appraisals
      summary: Update just the history section of an appraisal
      operationId: updateAppraisalHistory
      security:
        - BearerAuth: []
      parameters:
        - name: appraisalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerVehicleHistory"
      responses:
        "200":
          description: History section updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Appraisal"
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Appraisal not found

  /appraisals/{appraisalId}/condition:
    put:
      tags:
        - appraisals
      summary: Update just the condition section of an appraisal
      operationId: updateAppraisalCondition
      security:
        - BearerAuth: []
      parameters:
        - name: appraisalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/VehicleCondition"
      responses:
        "200":
          description: Condition section updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Appraisal"
        "400":
          description: Invalid input
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Appraisal not found

  /appraisals/{appraisalId}/complete:
    post:
      tags:
        - appraisals
      summary: Mark an appraisal as complete
      operationId: completeAppraisal
      security:
        - BearerAuth: []
      parameters:
        - name: appraisalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Appraisal marked as complete
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Appraisal"
        "400":
          description: Cannot mark as complete - missing required sections
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Appraisal not found

  /appraisals/{appraisalId}/award:
    post:
      tags:
        - appraisals
      summary: Award a value to an appraisal
      operationId: awardAppraisal
      security:
        - BearerAuth: []
      parameters:
        - name: appraisalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - awardedValue
              properties:
                awardedValue:
                  type: number
                  format: float
                notes:
                  type: string
      responses:
        "200":
          description: Appraisal awarded
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Appraisal"
        "400":
          description: Cannot award - appraisal is not complete
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Appraisal not found

  /appraisals/{appraisalId}/favorite:
    post:
      tags:
        - appraisals
      summary: Toggle favorite status of an appraisal
      operationId: toggleFavoriteAppraisal
      security:
        - BearerAuth: []
      parameters:
        - name: appraisalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Favorite status toggled
          content:
            application/json:
              schema:
                type: object
                properties:
                  appraisalId:
                    type: string
                    format: uuid
                  isFavorite:
                    type: boolean
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Appraisal not found

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from the /auth/verify-2fa endpoint
  schemas:
    UserLogin:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
        password:
          type: string
          format: password
          example: securePassword123
    TwoFactorChallengeResponse:
      type: object
      required:
        - temporaryToken
        - expiresIn
        - challengeType
      properties:
        temporaryToken:
          type: string
          description: Temporary token for 2FA verification
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        expiresIn:
          type: integer
          description: Token expiration time in seconds
          example: 300
        challengeType:
          type: string
          enum:
            - sms
            - email
            - authenticator
          example: sms
        maskedDestination:
          type: string
          description: Masked phone number or email where OTP was sent
          example: "********1234"
        availableMethods:
          type: array
          items:
            type: string
            enum:
              - sms
              - email
              - authenticator
          example:
            - sms
            - email
            - authenticator
    TwoFactorVerificationRequest:
      type: object
      required:
        - temporaryToken
        - otpCode
      properties:
        temporaryToken:
          type: string
          description: Temporary token from initial login
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        otpCode:
          type: string
          description: "OTP code from SMS, Email or Authenticator app"
          example: "123456"
    TwoFactorSetupResponse:
      type: object
      required:
        - secretKey
        - qrCodeUrl
      properties:
        secretKey:
          type: string
          description: Secret key for manual entry
          example: JBSWY3DPEHPK3PXP
        qrCodeUrl:
          type: string
          description: QR code URL for scanning
          example: "otpauth://totp/Example:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Example"
    ResendOTPRequest:
      type: object
      required:
        - intermediateToken
      properties:
        intermediateToken:
          type: string
          description: Temporary token received from login step
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        method:
          type: string
          enum:
            - sms
            - email
          description: "Method to receive OTP (optional, defaults to previous method)"
          example: sms
    ResendOTPResponse:
      type: object
      required:
        - success
        - message
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: OTP sent successfully
        twoFactorMethod:
          type: string
          enum:
            - sms
            - email
          description: The method used to send the OTP
          example: sms
    AuthResponse:
      type: object
      required:
        - accessToken
        - refreshToken
        - expiresIn
      properties:
        accessToken:
          type: string
          description: JWT access token to be used for API authentication
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        refreshToken:
          type: string
          description: Token used to obtain a new access token
          example: def502008a345df...
        expiresIn:
          type: integer
          description: Token expiration time in seconds
          example: 86400
        tokenType:
          type: string
          example: Bearer
        user:
          $ref: "#/components/schemas/UserProfile"
    UserProfile:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 550e8400-e29b-41d4-a716-************
        email:
          type: string
          format: email
          example: <EMAIL>
        name:
          type: string
          example: John Doe
        phoneNumber:
          type: string
          example: "+15555551234"
        twoFactorEnabled:
          type: boolean
          example: true
        role:
          type: string
          enum:
            - ADMIN
            - MANAGER
            - SALESPERSON
        dealerships:
          type: array
          items:
            $ref: "#/components/schemas/DealershipReference"
        twoFactorMethods:
          type: array
          items:
            type: string
            enum:
              - sms
              - email
              - authenticator
          example:
            - sms
            - authenticator
        profilePhotoUrl:
          type: string
          format: uri
          description: S3 URL for user's profile photo
          example: "https://user-profile-photos.s3.amazonaws.com/550e8400-e29b-41d4-a716-************/profile.jpg"
        createdAt:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
    DeviceRegistration:
      type: object
      required:
        - deviceId
        - deviceName
        - deviceOs
        - deviceOsVersion
        - appVersion
        - appBuildNumber
      properties:
        deviceId:
          type: string
          description: Unique identifier for the device
          example: D3A2F6B1-8D3E-4C5A-9F7B-1A2B3C4D5E6F
        fcmToken:
          type: string
          description: Firebase Cloud Messaging registration token
          example: "fLD9lkhVM0WdfsXK2j10Br:APA91bHyxw..."
        deviceName:
          type: string
          description: Human-readable device name
          example: iPhone 15 Pro
        deviceOs:
          type: string
          description: Operating system name
          example: iOS
          enum:
            - iOS
            - Android
            - iPadOS
            - other
        deviceOsVersion:
          type: string
          description: Operating system version
          example: 17.0.1
        appVersion:
          type: string
          description: Application version
          example: 2.1.0
        appBuildNumber:
          type: string
          description: Application build number
          example: "123"
        lastLoginIp:
          type: string
          description: IP address of last login (automatically captured)
          example: ***********
    DeviceResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Server-assigned device record ID
          example: 550e8400-e29b-41d4-a716-************
        deviceId:
          type: string
          description: Unique identifier for the device
          example: D3A2F6B1-8D3E-4C5A-9F7B-1A2B3C4D5E6F
        fcmToken:
          type: string
          description: Firebase Cloud Messaging registration token
          example: "fLD9lkhVM0WdfsXK2j10Br:APA91bHyxw..."
        deviceName:
          type: string
          description: Human-readable device name
          example: iPhone 15 Pro
        deviceOs:
          type: string
          description: Operating system name
          example: iOS
        deviceOsVersion:
          type: string
          description: Operating system version
          example: 17.0.1
        appVersion:
          type: string
          description: Application version
          example: 2.1.0
        appBuildNumber:
          type: string
          description: Application build number
          example: "123"
        lastLoginIp:
          type: string
          description: IP address of last login
          example: ***********
        lastLoginDate:
          type: string
          format: date-time
          description: Date and time of last login
          example: "2023-01-01T12:00:00Z"
        createdAt:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
    DrivingLicense:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the document record
          example: 550e8400-e29b-41d4-a716-************
        licenseNumber:
          type: string
          description: Driving license number
          example: **********
        expiryDate:
          type: string
          format: date
          description: License expiry date (YYYY-MM-DD)
          example: "2025-12-31"
        issuingCountry:
          type: string
          description: Country that issued the license
          example: United States
        issuingState:
          type: string
          description: State that issued the license (if applicable)
          example: California
        category:
          type: string
          description: License category/class
          example: B
        fullName:
          type: string
          description: Full name as it appears on license
          example: John Smith
        dateOfBirth:
          type: string
          format: date
          description: Date of birth (YYYY-MM-DD)
          example: "1990-01-15"
        issueDate:
          type: string
          format: date
          description: License issue date (YYYY-MM-DD)
          example: "2020-01-15"
        frontImageUrl:
          type: string
          format: uri
          description: S3 URL for the front image of the license
          example: "https://user-documents.s3.amazonaws.com/driving-licenses/front/550e8400-e29b-41d4-a716-************.jpg"
        backImageUrl:
          type: string
          format: uri
          description: S3 URL for the back image of the license
          example: "https://user-documents.s3.amazonaws.com/driving-licenses/back/550e8400-e29b-41d4-a716-************.jpg"
        verificationStatus:
          type: string
          enum:
            - pending
            - verified
            - rejected
          description: Status of license verification
          example: pending
        verificationRejectionReason:
          type: string
          description: Reason for rejection if status is rejected
          example: Image unclear or document expired
        createdAt:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
    DrivingLicenseInput:
      type: object
      required:
        - licenseNumber
        - expiryDate
        - issuingCountry
      properties:
        licenseNumber:
          type: string
          description: Driving license number
          example: **********
        expiryDate:
          type: string
          format: date
          description: License expiry date (YYYY-MM-DD)
          example: "2025-12-31"
        issuingCountry:
          type: string
          description: Country that issued the license
          example: United States
        issuingState:
          type: string
          description: State that issued the license (if applicable)
          example: California
        category:
          type: string
          description: License category/class
          example: B
        fullName:
          type: string
          description: Full name as it appears on license
          example: John Smith
        dateOfBirth:
          type: string
          format: date
          description: Date of birth (YYYY-MM-DD)
          example: "1990-01-15"
        issueDate:
          type: string
          format: date
          description: License issue date (YYYY-MM-DD)
          example: "2020-01-15"
    ProfilePhotoResponse:
      type: object
      required:
        - success
        - message
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: Profile photo updated successfully
        photoUrl:
          type: string
          format: uri
          description: S3 URL to access the profile photo
          example: "https://api.example.com/v1/profile/photo"
    DealershipReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
    Dealership:
      type: object
      required:
        - id
        - name
        - address
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the dealership
        name:
          type: string
          description: Name of the dealership
        longName:
          type: string
          description: Extended Name of the dealership
        brand:
          $ref: "#/components/schemas/Brand"
          description: brand of dealership
        address:
          type: string
          description: Full address of the dealership
        phoneNumber:
          type: string
          description: Contact phone number for the dealership
        email:
          type: string
          format: email
          description: Contact email for the dealership
        city:
          type: string
        state:
          type: string
        postcode:
          type: string
        phone:
          type: string
        website:
          type: string
        DriveTermConditions:
          type: string
          description: Terms for drives
        carLoanTermConditions:
          type: string
          description: Terms for car loan
        logo:
          type: string
          format: uri
          description: S3 URL for dealership logo
          example: "https://photos.s3.amazonaws.com/550e8400-e29b-41d4-a716-************/logo.jpg"
        settingDistanceUnit:
          type: string
          description: Kms or miles
        settingDateFormat:
          type: string
          description: Date format like dd/mm/yyyy
        settingTimezone:
          type: string
          description: Timezone
        advanceBookingEnabled:
          type: boolean
          description: true or false
        groups:
          type: array
          items:
            $ref: "#/components/schemas/Group"
          description: dealership groups data
        properties:
          type: array
          items:
            $ref: "#/components/schemas/Property"
          description: name-value properties
    Brand:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the brand
        name:
          type: string
          description: Name of the brand
        logo:
          type: string
          format: uri
          description: S3 URL for brand logo
          example: "https://photos.s3.amazonaws.com/550e8400-e29b-41d4-a716-************/brand.jpg"
    Group:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the group
        name:
          type: string
          description: Name of the group
    Property:
      type: object
      required:
        - id
        - name
        - value
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the property
        name:
          type: string
          description: Name of the property
        value:
          type: string
          description: Value of the property
    Vehicle:
      type: object
      required:
        - id
        - registrationNumber
        - make
        - model
        - year
        - color
        - lastOdometerReading
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the vehicle
        registrationNumber:
          type: string
          description: Registration/license plate number
        registrationExpiry:
          type: string
          description: Registration expiry date
        make:
          type: string
          description: "Make of the vehicle (e.g., Toyota, Ford)"
        brand:
          $ref: "#/components/schemas/Brand"
          description: brand of dealership
        model:
          type: string
          description: "Model of the vehicle (e.g., Corolla, F-150)"
        year:
          type: integer
          description: Year of manufacture
        color:
          type: string
          description: Color of the vehicle
        vin:
          type: string
          description: vin of the vehicle
        stockNumber:
          type: string
          description: stock Number of the vehicle
        lastOdometerReading:
          type: number
          description: Last recorded odometer reading in kilometers
        lastFuelGaugeLevel:
          type: number
          description: Last recorded Fuel Gauge Level reading
        lastKnownLocation:
          $ref: "#/components/schemas/GPSLocation"
          description: Last recorded GPS location
        lastSystemInspectionTimestamp:
          type: string
          description: Last system inspection time
        lastDamageReport:
          $ref: "#/components/schemas/DamageReport"
          description: Damage report last captured
        properties:
          type: array
          items:
            $ref: "#/components/schemas/Property"
          description: name-value properties
        images:
          type: array
          items:
            type: string
            format: uri
          description: URLs to images of the vehicle
        availableForDrive:
          type: boolean
          description: Whether the vehicle is available for drive
        type:
          type: string
          enum:
            - new
            - demo
            - old
          description: Type of the vehicle
    DamageReport:
      type: object
      properties:
        description:
          type: string
          description: Text description of the damage
        photos:
          type: array
          items:
            type: string
            format: uri
          description: URLs to damaged areas of the vehicle
        timestamp:
          type: string
          format: date-time
          description: When the damage was reported
    GPSLocation:
      type: object
      required:
        - latitude
        - longitude
        - timestamp
      properties:
        latitude:
          type: number
          format: double
          description: Latitude coordinate
        longitude:
          type: number
          format: double
          description: Longitude coordinate
        timestamp:
          type: string
          format: date-time
          description: Time when the location was recorded
        accuracy:
          type: number
          description: Accuracy of the GPS reading in meters
    Customer:
      type: object
      required:
        - name
        - email
        - phoneNumber
        - dealershipId
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the customer
        dealershipId:
          type: string
          format: uuid
        company:
          type: string
        name:
          type: string
          description: Full name of the customer
        age:
          type: integer
          minimum: 18
          description: Age of the customer
        address:
          type: string
        city:
          type: string
        state:
          type: string
        postcode:
          type: string
        drivingLicense:
          $ref: "#/components/schemas/DrivingLicense"
          description: Customer's driving license
        email:
          type: string
          format: email
          description: Customer's email address
        phoneNumber:
          type: string
          description: Customer's mobile number
        signature:
          type: string
          format: binary
          description: Customer's signature for the drive agreement
    Booking:
      type: object
      required:
        - id
        - dealershipId
        - expectedPickupDateTime
        - expectedReturnDateTime
        - vehicleId
        - customerId
        - salesPersonId
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the drive
        dealershipId:
          type: string
          format: uuid
          description: ID of the dealership
        vehicleId:
          type: string
          format: uuid
          description: ID of the vehicle being driven
        customerId:
          type: string
          format: uuid
          description: ID of the customer taking the drive
        salesPersonId:
          type: string
          format: uuid
          description: ID of the salesperson managing the drive
        expectedPickupDateTime:
          type: string
          format: date-time
          description: Expected pickup date and time
        expectedReturnDateTime:
          type: string
          format: date-time
          description: Expected return date and time
        status:
          type: string
          enum:
            - scheduled
            - completed
            - canceled
          description: Current status of the booking
    Drive:
      type: object
      required:
        - id
        - dealershipId
        - vehicleId
        - customerId
        - salesPersonId
        - startOdometerReading
        - expectedReturnDateTime
        - startDateTime
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the drive
        dealershipId:
          type: string
          format: uuid
          description: ID of the dealership
        vehicleId:
          type: string
          format: uuid
          description: ID of the vehicle being driven
        customerId:
          type: string
          format: uuid
          description: ID of the customer taking the drive
        salesPersonId:
          type: string
          format: uuid
          description: ID of the salesperson managing the drive
        startDateTime:
          type: string
          format: date-time
          description: Start date and time of the drive
        endDateTime:
          type: string
          format: date-time
          description: End date and time of the drive (null if ongoing)
        expectedReturnDateTime:
          type: string
          format: date-time
          description: Expected return date and time
        startOdometerReading:
          type: number
          description: Odometer reading at the start in kilometers
        endOdometerReading:
          type: number
          description: Odometer reading at the end in kilometers
        initialDamageReport:
          $ref: "#/components/schemas/DamageReport"
          description: Damage report before the drive
        finalDamageReport:
          $ref: "#/components/schemas/DamageReport"
          description: Damage report after the drive
        salesPersonAccompanying:
          type: boolean
          description: Whether the salesperson accompanies the customer
        routeData:
          type: array
          items:
            $ref: "#/components/schemas/GPSLocation"
          description: GPS route data if salesperson is accompanying
        agreementAccepted:
          type: boolean
          description: Whether the customer accepted the agreement
        notes:
          type: string
          description: Additional notes about the drive
        status:
          type: string
          enum:
            - scheduled
            - active
            - completed
            - canceled
          description: Current status of the drive
        sold-status:
          type: string
          enum:
            - new
            - demo
            - user
          description: Current sold status of the vehicle
    BookingUpdateRequest:
      type: object
      required:
        - vehicleId
        - expectedPickupDateTime
        - expectedReturnDateTime
        - salesPersonId
      properties:
        vehicleId:
          type: string
          format: uuid
          description: ID of the vehicle selected for drive
        driveType:
          type: string
          enum:
            - test-drive
            - loan
        customerId:
          type: string
          format: uuid
          description: ID of an existing customer
        customerInfo:
          $ref: "#/components/schemas/Customer"
          description: New customer information (not required if customerId is provided)
        expectedPickupDateTime:
          type: string
          format: date-time
          description: Expected pickup date and time
        expectedReturnDateTime:
          type: string
          format: date-time
          description: Expected return date and time
        salesPersonId:
          type: string
          description: user Id of sales person
    BookingInitRequest:
      type: object
      required:
        - dealershipId
        - vehicleId
        - driveType
        - expectedPickupDateTime
        - expectedReturnDateTime
        - salesPersonId
      properties:
        dealershipId:
          type: string
          format: uuid
          description: ID of the dealership where the drive will take place
        vehicleId:
          type: string
          format: uuid
          description: ID of the vehicle selected for drive
        driveType:
          type: string
          enum:
            - test-drive
            - loan
        customerId:
          type: string
          format: uuid
          description: ID of an existing customer
        customerInfo:
          $ref: "#/components/schemas/Customer"
          description: New customer information (not required if customerId is provided)
        expectedPickupDateTime:
          type: string
          format: date-time
          description: Expected pickup date and time
        expectedReturnDateTime:
          type: string
          format: date-time
          description: Expected return date and time
        salesPersonId:
          type: string
          description: user Id of sales person
        notes:
          type: string
          description: notes about the booking
    DriveInitRequest:
      type: object
      required:
        - dealershipId
        - vehicleId
        - driveType
      properties:
        dealershipId:
          type: string
          format: uuid
          description: ID of the dealership where the drive will take place
        vehicleId:
          type: string
          format: uuid
          description: ID of the vehicle selected for drive
        bookingId:
          type: string
          format: uuid
          description: Booking Id from which the Drive is initiated
        driveType:
          type: string
          enum:
            - test-drive
            - enquiry
            - loan
    DriveCustomerUpdateRequest:
      type: object
      properties:
        customerId:
          type: string
          format: uuid
          description: ID of an existing customer
        customerInfo:
          $ref: "#/components/schemas/Customer"
          description: New customer information (not required if customerId is provided)
    DriveReturnTimeUpdateRequest:
      type: object
      required:
        - expectedReturnDateTime
      properties:
        expectedReturnDateTime:
          type: string
          format: date-time
          description: Expected return date and time
    DriveDamageUpdateRequest:
      type: object
      required:
        - description
      properties:
        description:
          type: string
          description: Text description of the damage
    DriveOdometerUpdateRequest:
      type: object
      required:
        - odometerReading
      properties:
        odometerReading:
          type: number
          description: Current odometer reading in kilometers
    DriveReassignRequest:
      type: object
      required:
        - salesPersonId
      properties:
        salesPersonId:
          type: string
          description: user Id of new sales person
    DriveMarkSoldRequest:
      type: object
      required:
        - soldType
        - sold
      properties:
        sold:
          type: boolean
          description: mark sold or reset
        soldType:
          type: string
          enum:
            - new
            - demo
            - used
          description: Type of sold
          example: new
    DriveAgreementUpdateRequest:
      type: object
      required:
        - agreementAccepted
      properties:
        agreementAccepted:
          type: boolean
          description: Whether customer accepted the drive agreement
    DriveStartRequest:
      type: object
      required:
        - salesPersonAccompanying
      properties:
        salesPersonAccompanying:
          type: boolean
          description: Whether the salesperson will accompany the customer
        notes:
          type: string
          description: Additional notes about the drive start
    DriveCompletionRequest:
      type: object
      required:
        - driveId
        - endOdometerReading
      properties:
        driveId:
          type: string
          format: uuid
          description: ID of the drive to complete
        endOdometerReading:
          type: number
          description: Final odometer reading in kilometers
        finalDamageReport:
          $ref: "#/components/schemas/DamageReport"
          description: Final damage report if any
        notes:
          type: string
          description: Notes about the drive
    AppraisalSummary:
      type: object
      properties:
        id:
          type: string
          format: uuid
        dealershipId:
          type: string
          format: uuid
        dealershipName:
          type: string
        customerName:
          type: string
        vehicleBrand:
          type: string
        vehicleModel:
          type: string
        registrationNumber:
          type: string
        status:
          type: string
          enum:
            - INCOMPLETE
            - COMPLETE
            - AWARDED
            - ARCHIVED
            - DELETED
        isFavorite:
          type: boolean
          default: false
        isMyAppraisal:
          type: boolean
          default: false
        completionPercentage:
          type: integer
          minimum: 0
          maximum: 100
        awardedValue:
          type: number
          format: float
        price:
          type: number
          format: float
        givenPrice:
          type: number
          format: float
        salesPersonId:
          type: string
          format: uuid
        thumbnailImageUrl:
          type: string
          format: uri
          description: S3 URL for the thumbnail image
          example: "https://documents.s3.amazonaws.com/appraisals/550e8400-e29b-41d4-a716-************.jpg"
        createdBy:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    AppraisalCreate:
      type: object
      required:
        - dealershipId
      properties:
        dealershipId:
          type: string
          format: uuid
        customer:
          oneOf:
            - type: object
              properties:
                id:
                  type: string
                  format: uuid
            - $ref: "#/components/schemas/Customer"
    Appraisal:
      allOf:
        - $ref: "#/components/schemas/AppraisalSummary"
        - type: object
          properties:
            customer:
              $ref: "#/components/schemas/Customer"
            vehicleDetails:
              $ref: "#/components/schemas/CustomerVehicleDetails"
            financeDetails:
              $ref: "#/components/schemas/FinanceDetails"
            optionsFitted:
              $ref: "#/components/schemas/OptionsFitted"
            vehicleHistory:
              $ref: "#/components/schemas/CustomerVehicleHistory"
            vehicleCondition:
              $ref: "#/components/schemas/VehicleCondition"
            vehicleImages:
              type: array
              items:
                $ref: "#/components/schemas/Image"
            serviceBookImages:
              type: array
              items:
                $ref: "#/components/schemas/Image"
            conditionImages:
              type: array
              items:
                $ref: "#/components/schemas/Image"
            awardedNotes:
              type: string
            updatedBy:
              type: string

    VehicleCondition:
      type: object
      properties:
        isClean:
          type: boolean
          default: true
        isWet:
          type: boolean
          default: false
        panelRating:
          type: integer
          minimum: 1
          maximum: 5
        paintworkRating:
          type: integer
          minimum: 1
          maximum: 5
        interiorRating:
          type: integer
          minimum: 1
          maximum: 5
        windscreenRating:
          type: integer
          minimum: 1
          maximum: 5
        isRoadTested:
          type: boolean
          default: false
        hasSignsOfRepair:
          type: boolean
          default: false
        repairDetails:
          type: string
        bodyPartConditions:
          type: array
          items:
            $ref: "#/components/schemas/BodyPartCondition"
        conditionImageIds:
          type: array
          items:
            type: string
            format: uuid
        additionalNotes:
          type: string

    BodyPartCondition:
      type: object
      properties:
        partName:
          type: string
        hasDamage:
          type: boolean
          default: false
        damageType:
          type: string
          enum:
            - SCRATCH
            - DENT
            - CHIP
            - RUST
            - CRACK
            - OTHER
        damageDescription:
          type: string
        damageImageIds:
          type: array
          items:
            type: string
            format: uuid

    CustomerVehicleDetails:
      type: object
      properties:
        brand:
          type: string
        model:
          type: string
        registrationNumber:
          type: string
        isVehiclePresent:
          type: boolean
          default: true
        registrationState:
          type: string
        buildMonth:
          type: integer
          minimum: 1
          maximum: 12
        buildYear:
          type: integer
          minimum: 1900
        vinNumber:
          type: string
        complianceMonth:
          type: integer
          minimum: 1
          maximum: 12
        complianceYear:
          type: integer
          minimum: 1900
        odometerDate:
          type: string
          format: date
        odometerReading:
          type: integer
        exteriorColor:
          type: string
        interiorColor:
          type: string
        seatType:
          type: string
          enum:
            - LEATHER
            - CLOTH
            - MIXED
        registrationExpiry:
          type: string
          format: date
        engineSize:
          type: string
        engineNumber:
          type: string
        fuelType:
          type: string
          enum:
            - PETROL
            - DIESEL
            - ELECTRIC
            - HYBRID
            - PLUGIN_HYBRID
            - LPG
            - OTHER
        drivingWheels:
          type: string
          enum:
            - FWD
            - RWD
            - AWD
            - 4WD
        wheelSize:
          type: string
        spareWheelType:
          type: string
          enum:
            - FULL_SIZE
            - SPACE_SAVER
            - RUN_FLAT
            - REPAIR_KIT
            - NONE
        transmission:
          type: string
          enum:
            - MANUAL
            - AUTOMATIC
            - CVT
            - SEMI_AUTOMATIC
            - DUAL_CLUTCH
        numberOfDoors:
          type: integer
        numberOfSeats:
          type: integer
        bodyType:
          type: string
          enum:
            - SEDAN
            - HATCHBACK
            - WAGON
            - SUV
            - COUPE
            - CONVERTIBLE
            - UTE
            - VAN
            - TRUCK
            - OTHER
        redbookCode:
          type: string
        images:
          type: array
          items:
            $ref: "#/components/schemas/Image"
    CustomerVehicleHistory:
      type: object
      properties:
        numberOfOwners:
          type: integer
        hasAccidentHistory:
          type: boolean
          default: false
        accidentDetails:
          type: string
        lastServiceDate:
          type: string
          format: date
        lastServiceOdometer:
          type: integer
        nextServiceDue:
          type: string
          format: date
        hasDashWarningLights:
          type: boolean
          default: false
        dashWarningDetails:
          type: string
        serviceBookImageIds:
          type: array
          items:
            type: string
            format: uuid
    OptionsFitted:
      type: object
      properties:
        hasSunroof:
          type: boolean
          default: false
        hasTintedWindows:
          type: boolean
          default: false
        hasTowbar:
          type: boolean
          default: false
        hasKeylessEntry:
          type: boolean
          default: false
        hasBluetooth:
          type: boolean
          default: false
        hasVentilatedSeats:
          type: boolean
          default: false
        hasTrayFitted:
          type: boolean
          default: false
        hasCanopyFitted:
          type: boolean
          default: false
        hasAftermarketWheels:
          type: boolean
          default: false
        hasBullBar:
          type: boolean
          default: false
        hasExtendedWarranty:
          type: boolean
          default: false
        extendedWarrantyExpiry:
          type: string
          format: date
        ppsr:
          type: boolean
          default: false
        additionalOptions:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              description:
                type: string
    FinanceDetails:
      type: object
      properties:
        isFinanced:
          type: boolean
          default: false
        financeCompany:
          type: string
        currentRepaymentAmount:
          type: number
          format: float
        termsMonths:
          type: integer
        interestRate:
          type: number
          format: float
        nextDueDate:
          type: string
          format: date
        hasClearTitle:
          type: boolean
    Image:
      type: object
      properties:
        id:
          type: string
          format: uuid
        url:
          type: string
        thumbnailUrl:
          type: string
    TradePlate:
      type: object
      required:
        - uuid
        - number
        - status
        - expired
        - in_use
        - created_at
        - updated_at
      properties:
        uuid:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
          description: Unique identifier for the trade plate
        number:
          type: string
          example: "TP001"
          description: Trade plate number
        expiry:
          type: string
          format: date
          nullable: true
          example: "2024-12-31"
          description: Expiry date of the trade plate
        status:
          type: string
          enum:
            - active
            - inactive
          example: "active"
          description: Current status of the trade plate
        expired:
          type: boolean
          example: false
          description: Flag indicating if the trade plate is expired
        in_use:
          type: boolean
          example: false
          description: Flag indicating if the trade plate is currently being used by an in-progress drive
        created_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
          description: Timestamp when the trade plate was created
        updated_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
          description: Timestamp when the trade plate was last updated
    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          example: 401
        message:
          type: string
          example: Invalid credentials

class AddExtraFieldsToUsers < ActiveRecord::Migration[8.0]
  def up
    add_column :users, :first_name, :string
    add_column :users, :last_name, :string
    add_column :users, :uuid, :string, null: false
    add_column :users, :otp, :integer
    add_column :users, :otp_generated_at, :datetime
    add_column :users, :preferred_2fa, :integer, limit: 1, null: false, default: 2
    add_column :users, :phone, :string

    add_index :users, :uuid, unique: true

    say_with_time "Setting UUIDs for existing users" do
      User.reset_column_information
      User.where(uuid: nil).find_each do |user|
        user.update_columns(uuid: SecureRandom.uuid)
      end
    end
  end

  def down
    remove_index :users, :uuid
    remove_column :users, :first_name
    remove_column :users, :last_name
    remove_column :users, :phone
    remove_column :users, :preferred_2fa
    remove_column :users, :otp_generated_at
    remove_column :users, :otp
    remove_column :users, :uuid
  end
end

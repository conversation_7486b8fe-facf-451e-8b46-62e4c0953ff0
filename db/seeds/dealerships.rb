# Create Dealership Groups
puts "Creating dealership groups..."

group1 = DealershipGroup.create!(
  name: "AutoNation Australia",
  description: "Australia's largest automotive retail group",
  signup_code: "AUTO2024",
  status: :active,
  contract_end_date: 1.year.from_now
)

group2 = DealershipGroup.create!(
  name: "DriveTime Motors",
  description: "Independent dealership network",
  signup_code: "DRIVE2024",
  status: :active,
  contract_end_date: 2.years.from_now
)

# Create Dealerships
puts "Creating dealerships..."

# Dealerships for AutoNation
dealership1 = Dealership.create!(
  name: "AutoNation Toyota Melbourne",
  long_name: "AutoNation Toyota Melbourne CBD",
  status: :active,
  abn: "***********",
  address_line1: "123 Swanston Street",
  suburb: "Melbourne",
  state: "Victoria",
  postcode: "3000",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  website: "https://www.autonation.com.au",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: :melbourne,
  setting_distance_unit: :kilometers,
  dealership_group: group1
)

# Create settings for dealership1
dealership1.create_dealership_features_setting!(
  advance_booking_enabled: true,
  insurance_waiver_enabled: true,
  dealer_drive_subscription: true,
  appraisals_subscription: true,
  setting_recent_customer_age: 90
)

dealership1.create_dealership_email_setting!(
  send_test_drive_review_email: true,
  send_test_drive_terms_email: true,
  send_loan_terms_email: true,
  loan_review_email_enabled: true,
  send_email_for_bookings: :both,
  level1_odometer_warning_km: 1000,
  level1_odometer_warning_email: "<EMAIL>",
  level2_odometer_warning_km: 2000,
  level2_odometer_warning_email: "<EMAIL>",
  email_from_address: "<EMAIL>",
  email_display_name: "AutoNation Toyota Melbourne"
)

dealership1.create_dealership_terms_setting!(
  insurance_waiver_text: "By proceeding with the test drive, you agree to our insurance terms."
)

dealership1.trade_plates.create!(
  number: "TP001",
  expiry: 1.year.from_now,
  status: :active
)

dealership1.trade_plates.create!(
  number: "TP002",
  expiry: 1.day.ago,
  status: :active
)

dealership1.vehicles.create!(
  make: "Toyota",
  model: "Camry",
  year: 2020,
  rego: "ABC123",
  vin: "***********234567",
  stock_number: "T001",
  color: "Red",
  rego_expiry: 1.year.from_now,
  is_trade_plate_used: false,
  last_known_odometer_km: 10000,
  last_known_fuel_gauge_level: 50,
  last_system_inspection_timestamp: 1.month.ago,
  status: :available,
  available_for_drive: true,
  vehicle_type: :new_vehicle
)

dealership1.vehicles.create!(
  make: "Toyota",
  model: "Corolla",
  year: 2021,
  vin: "***********345678",
  stock_number: "T002",
  color: "Blue",
  is_trade_plate_used: true,
  last_known_odometer_km: 15000,
  last_known_fuel_gauge_level: 60,
  last_system_inspection_timestamp: 1.month.ago,
  status: :available,
  available_for_drive: true,
  vehicle_type: :demo
)

dealership1.vehicles.create!(
  make: "Toyota",
  model: "Rav4",
  year: 2022,
  rego: "DEF456",
  vin: "***********456789",
  stock_number: "T003",
  color: "Green",
  rego_expiry: 1.year.ago,
  is_trade_plate_used: false,
  last_known_odometer_km: 20000,
  last_known_fuel_gauge_level: 70,
  last_system_inspection_timestamp: 1.month.ago,
  status: :available,
  available_for_drive: false,
  vehicle_type: :old
)

dealership2 = Dealership.create!(
  name: "AutoNation Honda Sydney",
  long_name: "AutoNation Honda Sydney Central",
  status: :active,
  abn: "***********",
  address_line1: "456 George Street",
  suburb: "Sydney",
  state: "New South Wales",
  postcode: "2000",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: :sydney,
  setting_distance_unit: :kilometers,
  dealership_group: group1
)

# Create settings for dealership2
dealership2.create_dealership_features_setting!(
  advance_booking_enabled: true,
  insurance_waiver_enabled: true,
  dealer_drive_subscription: true,
  appraisals_subscription: true,
  setting_recent_customer_age: 90
)

dealership2.create_dealership_email_setting!(
  send_test_drive_review_email: true,
  send_test_drive_terms_email: true,
  send_loan_terms_email: true,
  loan_review_email_enabled: true,
  send_email_for_bookings: :both,
  level1_odometer_warning_km: 1000,
  level1_odometer_warning_email: "<EMAIL>",
  level2_odometer_warning_km: 2000,
  level2_odometer_warning_email: "<EMAIL>",
  email_from_address: "<EMAIL>",
  email_display_name: "AutoNation Honda Sydney"
)

dealership2.create_dealership_terms_setting!(
  insurance_waiver_text: "By proceeding with the test drive, you agree to our insurance terms."
)

dealership2.trade_plates.create!(
  number: "TPI001",
  expiry: 1.year.from_now,
  status: :active
)

# Dealerships for DriveTime
dealership3 = Dealership.create!(
  name: "DriveTime Perth",
  long_name: "DriveTime Perth CBD",
  status: :active,
  abn: "***********",
  address_line1: "789 Hay Street",
  suburb: "Perth",
  state: "Western Australia",
  postcode: "6000",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: :perth,
  setting_distance_unit: :kilometers,
  dealership_group: group2
)

# Create settings for dealership3
dealership3.create_dealership_features_setting!(
  advance_booking_enabled: false,
  insurance_waiver_enabled: false,
  dealer_drive_subscription: false,
  appraisals_subscription: false,
  setting_recent_customer_age: 60
)

dealership3.create_dealership_email_setting!(
  send_test_drive_review_email: false,
  send_test_drive_terms_email: false,
  send_loan_terms_email: false,
  loan_review_email_enabled: false,
  send_email_for_bookings: :none,
  email_from_address: "<EMAIL>",
  email_display_name: "DriveTime Perth"
)

dealership3.create_dealership_terms_setting!

# Create Dealership Alerts
puts "Creating dealership alerts..."

# Alerts for AutoNation Toyota Melbourne
DealershipAlert.create!(
  dealership: dealership1,
  alert_type: :warning,
  threshold: 5,
  emails: "<EMAIL>,<EMAIL>"
)

DealershipAlert.create!(
  dealership: dealership1,
  alert_type: :error,
  threshold: 10,
  emails: "<EMAIL>,<EMAIL>,<EMAIL>"
)

# Alerts for AutoNation Honda Sydney
DealershipAlert.create!(
  dealership: dealership2,
  alert_type: :warning,
  threshold: 5,
  emails: "<EMAIL>,<EMAIL>"
)

# Alerts for DriveTime Perth
DealershipAlert.create!(
  dealership: dealership3,
  alert_type: :info,
  threshold: 3,
  emails: "<EMAIL>"
)

# Create Customers for each dealership
puts "Creating customers..."

# Customers for AutoNation Toyota Melbourne
puts "Creating customers for AutoNation Toyota Melbourne..."

Customer.create!([
  {
    dealership: dealership1,
    first_name: "John",
    last_name: "Smith",
    email: "<EMAIL>",
    phone_number: "+61412345001",
    age: 35,
    gender: :male,
    company_name: "Smith & Associates",
    external_id: "CUST001",
    postcode: "3000",
    suburb: "Melbourne",
    address_line1: "123 Collins Street",
    address_line2: "Apt 15",
    city: "Melbourne",
    state: "Victoria",
    country: :au
  },
  {
    dealership: dealership1,
    first_name: "Sarah",
    last_name: "Johnson",
    email: "<EMAIL>",
    phone_number: "+61412345002",
    age: 28,
    gender: :female,
    postcode: "3141",
    suburb: "South Yarra",
    address_line1: "456 Toorak Road",
    city: "Melbourne",
    state: "Victoria",
    country: :au
  },
  {
    dealership: dealership1,
    first_name: "Michael",
    last_name: "Brown",
    email: "<EMAIL>",
    phone_number: "+61412345003",
    age: 42,
    gender: :male,
    company_name: "Brown Industries",
    external_id: "CUST002",
    postcode: "3182",
    suburb: "Malvern",
    address_line1: "789 High Street",
    city: "Melbourne",
    state: "Victoria",
    country: :au
  },
  {
    dealership: dealership1,
    first_name: "Emma",
    last_name: "Wilson",
    email: "<EMAIL>",
    phone_number: "+***********",
    age: 31,
    gender: :female,
    postcode: "3121",
    suburb: "Richmond",
    address_line1: "321 Swan Street",
    city: "Melbourne",
    state: "Victoria",
    country: :au
  },
  {
    dealership: dealership1,
    first_name: "David",
    last_name: "Taylor",
    email: "<EMAIL>",
    phone_number: "+***********",
    age: 39,
    gender: :male,
    company_name: "Taylor Consulting",
    postcode: "3006",
    suburb: "Southbank",
    address_line1: "654 Southbank Boulevard",
    city: "Melbourne",
    state: "Victoria",
    country: :au
  }
])

# Customers for AutoNation Honda Sydney
puts "Creating customers for AutoNation Honda Sydney..."

Customer.create!([
  {
    dealership: dealership2,
    first_name: "Jessica",
    last_name: "Anderson",
    email: "<EMAIL>",
    phone_number: "+***********",
    age: 26,
    gender: :female,
    postcode: "2000",
    suburb: "Sydney",
    address_line1: "123 George Street",
    address_line2: "Unit 8",
    city: "Sydney",
    state: "New South Wales",
    country: :au
  },
  {
    dealership: dealership2,
    first_name: "Robert",
    last_name: "Martinez",
    email: "<EMAIL>",
    phone_number: "+***********",
    age: 44,
    gender: :male,
    company_name: "Martinez Holdings",
    external_id: "SYD001",
    postcode: "2010",
    suburb: "Surry Hills",
    address_line1: "456 Crown Street",
    city: "Sydney",
    state: "New South Wales",
    country: :au
  },
  {
    dealership: dealership2,
    first_name: "Lisa",
    last_name: "Garcia",
    email: "<EMAIL>",
    phone_number: "+61423456003",
    age: 33,
    gender: :female,
    postcode: "2021",
    suburb: "Paddington",
    address_line1: "789 Oxford Street",
    city: "Sydney",
    state: "New South Wales",
    country: :au
  },
  {
    dealership: dealership2,
    first_name: "Christopher",
    last_name: "Lee",
    email: "<EMAIL>",
    phone_number: "+61423456004",
    age: 37,
    gender: :male,
    company_name: "Lee Enterprises",
    external_id: "SYD002",
    postcode: "2060",
    suburb: "North Sydney",
    address_line1: "321 Miller Street",
    city: "Sydney",
    state: "New South Wales",
    country: :au
  },
  {
    dealership: dealership2,
    first_name: "Amanda",
    last_name: "White",
    email: "<EMAIL>",
    phone_number: "+61423456005",
    age: 29,
    gender: :female,
    postcode: "2030",
    suburb: "Dover Heights",
    address_line1: "654 Old South Head Road",
    city: "Sydney",
    state: "New South Wales",
    country: :au
  },
  {
    dealership: dealership2,
    first_name: "Daniel",
    last_name: "Thompson",
    email: "<EMAIL>",
    phone_number: "+61423456006",
    age: 41,
    gender: :male,
    company_name: "Thompson & Co",
    postcode: "2040",
    suburb: "Leichhardt",
    address_line1: "987 Norton Street",
    city: "Sydney",
    state: "New South Wales",
    country: :au
  }
])

# Customers for DriveTime Perth
puts "Creating customers for DriveTime Perth..."

Customer.create!([
  {
    dealership: dealership3,
    first_name: "Michelle",
    last_name: "Davis",
    email: "<EMAIL>",
    phone_number: "+***********",
    age: 32,
    gender: :female,
    postcode: "6000",
    suburb: "Perth",
    address_line1: "123 Hay Street",
    city: "Perth",
    state: "Western Australia",
    country: :au
  },
  {
    dealership: dealership3,
    first_name: "James",
    last_name: "Miller",
    email: "<EMAIL>",
    phone_number: "+***********",
    age: 38,
    gender: :male,
    company_name: "Miller Motors",
    external_id: "PER001",
    postcode: "6008",
    suburb: "Subiaco",
    address_line1: "456 Rokeby Road",
    city: "Perth",
    state: "Western Australia",
    country: :au
  },
  {
    dealership: dealership3,
    first_name: "Nicole",
    last_name: "Rodriguez",
    email: "<EMAIL>",
    phone_number: "+***********",
    age: 27,
    gender: :female,
    postcode: "6009",
    suburb: "Mount Lawley",
    address_line1: "789 Beaufort Street",
    city: "Perth",
    state: "Western Australia",
    country: :au
  },
  {
    dealership: dealership3,
    first_name: "Kevin",
    last_name: "Clark",
    email: "<EMAIL>",
    phone_number: "+***********",
    age: 45,
    gender: :male,
    company_name: "Clark Industries",
    external_id: "PER002",
    postcode: "6005",
    suburb: "West Perth",
    address_line1: "321 Murray Street",
    city: "Perth",
    state: "Western Australia",
    country: :au
  }
])

puts "Customer seeds created successfully!"
puts "Total customers created: #{Customer.count}"
puts "Dealership Seed created successfully!"
